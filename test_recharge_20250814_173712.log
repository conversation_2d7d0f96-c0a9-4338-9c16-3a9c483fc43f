2025-08-14 17:37:12,691 - INFO - [充值流程] 开始普通充值检查 - 订单号: TEST_ORDER_001, 用户ID: 12345, 充值金额: 10.50元
2025-08-14 17:37:12,691 - INFO - [充值流程] 步骤1: 获取访问令牌
2025-08-14 17:37:12,691 - INFO - [充值流程] 步骤1成功: 获取到访问令牌
2025-08-14 17:37:12,692 - INFO - [充值流程] 步骤2: 查找订单中的buyer_eid
2025-08-14 17:37:12,692 - INFO - [充值流程] 步骤2成功: 找到订单信息, buyer_eid: test_buyer_eid_123
2025-08-14 17:37:12,692 - INFO - [用户检查] 开始检查用户资格 - 用户ID: 12345, buyer_eid: test_buyer_eid_123
2025-08-14 17:37:12,692 - INFO - [用户检查] 步骤1: 获取用户信息
2025-08-14 17:37:12,692 - INFO - [用户检查] 步骤1成功: 获取到用户信息
2025-08-14 17:37:12,692 - INFO - [用户检查] 步骤2: 检查用户余额 - 原始余额: 0, 转换后余额: 0.00元
2025-08-14 17:37:12,692 - INFO - [用户检查] 步骤2通过: 用户余额为0
2025-08-14 17:37:12,693 - INFO - [用户检查] 步骤3: 检查黑名单 - buyer_eid: test_buyer_eid_123
2025-08-14 17:37:12,693 - INFO - [用户检查] 步骤3通过: 不在黑名单中
2025-08-14 17:37:12,693 - INFO - [用户检查] 步骤4: 检查订单数限制 - 当前订单数: 0, 限制: 0
2025-08-14 17:37:12,693 - INFO - [用户检查] 步骤4通过: 订单数符合要求
2025-08-14 17:37:12,693 - INFO - [用户检查] 所有检查通过: 检查通过，符合新用户条件
2025-08-14 17:37:12,693 - INFO - [充值流程] 步骤3: 用户资格检查通过，显示确认对话框
2025-08-14 17:37:12,693 - INFO - [充值流程] 步骤4: 用户确认充值，开始执行充值操作
2025-08-14 17:37:12,693 - INFO - [普通充值] 开始执行充值操作 - 订单号: TEST_ORDER_001, 用户ID: 12345, 充值金额: 10.50元
2025-08-14 17:37:12,693 - INFO - [普通充值] 步骤1: 获取买家昵称和订单信息
2025-08-14 17:37:12,693 - INFO - [普通充值] 步骤1: 使用传入的买家昵称: 测试买家
2025-08-14 17:37:12,693 - INFO - [普通充值] 步骤1: 获取到订单信息, buyer_eid: test_buyer_eid_123
2025-08-14 17:37:12,693 - INFO - [普通充值] 步骤2: 获取访问令牌
2025-08-14 17:37:12,693 - INFO - [普通充值] 步骤2成功: 获取到访问令牌
2025-08-14 17:37:12,694 - INFO - [充值API] 开始调用充值API - 用户ID: 12345, 金额: 10.50元, 订单号: TEST_ORDER_001
2025-08-14 17:37:12,694 - INFO - [充值API] 请求URL: http://example.com/admin-api/kuaidi/mbr/user/recharge
2025-08-14 17:37:12,694 - INFO - [充值API] 请求数据: {'mbrId': 12345, 'balanceType': '1', 'balance': '10.50', 'changeReason': '1', 'bizType': 'balanceRecharge', 'remark': 'TEST_ORDER_001'}
2025-08-14 17:37:12,694 - INFO - [充值API] 发送充值请求...
2025-08-14 17:37:12,694 - INFO - [充值API] 响应状态码: 200
2025-08-14 17:37:12,694 - INFO - [充值API] 响应内容: {'code': 0, 'message': 'success'}
2025-08-14 17:37:12,694 - INFO - [普通充值] 步骤3结果: 成功, 消息: 充值成功
2025-08-14 17:37:12,694 - INFO - [普通充值] 步骤4: 充值成功，开始后续处理
2025-08-14 17:37:12,694 - INFO - [普通充值] 步骤4a: 保存buyer_eid到黑名单数据库
2025-08-14 17:37:12,694 - INFO - [普通充值] 步骤4a成功: buyer_eid test_buyer_eid_123 已保存到黑名单数据库
2025-08-14 17:37:12,694 - INFO - [普通充值] 步骤4b: 发送消息通知
2025-08-14 17:37:12,695 - INFO - [普通充值] 步骤4b成功: 消息通知发送完成
2025-08-14 17:37:12,695 - INFO - [普通充值] 充值流程完成
