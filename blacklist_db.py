import psycopg2
import logging
import configparser
from datetime import datetime
import json

# 读取配置文件
config = configparser.ConfigParser()
config.read('manual_recharge_config.ini', encoding='utf-8')

# 数据库配置
DB_HOST = config['Database']['host']
DB_PORT = config['Database']['port']
DB_USERNAME = config['Database']['username']
DB_PASSWORD = config['Database']['password']
DB_NAME = config['Database']['database']

class BlacklistDB:
    def __init__(self):
        self.connection = None
        self.connect()
        self.create_tables()
    
    def connect(self):
        """连接到PostgreSQL数据库"""
        try:
            self.connection = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USERNAME,
                password=DB_PASSWORD,
                database=DB_NAME
            )
            logging.info(f"成功连接到数据库 {DB_HOST}:{DB_PORT}/{DB_NAME}")
        except psycopg2.Error as e:
            logging.error(f"数据库连接失败: {e}")
            # 如果数据库不存在，尝试创建
            if "does not exist" in str(e):
                self.create_database()
            else:
                raise e
    
    def create_database(self):
        """创建数据库（如果不存在）"""
        try:
            # 连接到默认的postgres数据库来创建新数据库
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USERNAME,
                password=DB_PASSWORD,
                database='postgres'
            )
            conn.autocommit = True
            cursor = conn.cursor()
            
            # 创建数据库
            cursor.execute(f"CREATE DATABASE {DB_NAME}")
            logging.info(f"成功创建数据库 {DB_NAME}")
            
            cursor.close()
            conn.close()
            
            # 重新连接到新创建的数据库
            self.connect()
            
        except psycopg2.Error as e:
            logging.error(f"创建数据库失败: {e}")
            raise e
    
    def create_tables(self):
        """创建黑名单表"""
        try:
            cursor = self.connection.cursor()
            
            # 创建黑名单表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS blacklist (
                id SERIAL PRIMARY KEY,
                buyer_eid VARCHAR(255) UNIQUE NOT NULL,
                order_no VARCHAR(255),
                buyer_nick VARCHAR(255),
                user_id VARCHAR(100),
                recharge_amount DECIMAL(10,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT
            );
            
            -- 创建索引以提高查询性能
            CREATE INDEX IF NOT EXISTS idx_buyer_eid ON blacklist(buyer_eid);
            CREATE INDEX IF NOT EXISTS idx_order_no ON blacklist(order_no);
            CREATE INDEX IF NOT EXISTS idx_created_at ON blacklist(created_at);
            """
            
            cursor.execute(create_table_sql)
            self.connection.commit()
            logging.info("黑名单表创建成功")
            
            cursor.close()
            
        except psycopg2.Error as e:
            logging.error(f"创建表失败: {e}")
            self.connection.rollback()
            raise e
    
    def add_to_blacklist(self, buyer_eid, order_no=None, buyer_nick=None, user_id=None, recharge_amount=None, notes=None):
        """添加buyer_eid到黑名单"""
        try:
            cursor = self.connection.cursor()
            
            # 使用ON CONFLICT来处理重复的buyer_eid
            insert_sql = """
            INSERT INTO blacklist (buyer_eid, order_no, buyer_nick, user_id, recharge_amount, notes, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
            ON CONFLICT (buyer_eid) 
            DO UPDATE SET 
                order_no = EXCLUDED.order_no,
                buyer_nick = EXCLUDED.buyer_nick,
                user_id = EXCLUDED.user_id,
                recharge_amount = EXCLUDED.recharge_amount,
                notes = EXCLUDED.notes,
                updated_at = CURRENT_TIMESTAMP
            RETURNING id;
            """
            
            cursor.execute(insert_sql, (buyer_eid, order_no, buyer_nick, user_id, recharge_amount, notes))
            result = cursor.fetchone()
            self.connection.commit()
            
            if result:
                logging.info(f"成功添加/更新黑名单记录: buyer_eid={buyer_eid}, order_no={order_no}, id={result[0]}")
                return result[0]
            
            cursor.close()
            
        except psycopg2.Error as e:
            logging.error(f"添加黑名单记录失败: {e}")
            self.connection.rollback()
            raise e
    
    def is_in_blacklist(self, buyer_eid):
        """检查buyer_eid是否在黑名单中"""
        try:
            cursor = self.connection.cursor()
            
            select_sql = "SELECT id, created_at FROM blacklist WHERE buyer_eid = %s"
            cursor.execute(select_sql, (buyer_eid,))
            result = cursor.fetchone()
            
            cursor.close()
            
            if result:
                logging.info(f"buyer_eid {buyer_eid} 在黑名单中，记录ID: {result[0]}, 创建时间: {result[1]}")
                return True
            else:
                return False
                
        except psycopg2.Error as e:
            logging.error(f"查询黑名单失败: {e}")
            return False
    
    def get_blacklist_stats(self):
        """获取黑名单统计信息"""
        try:
            cursor = self.connection.cursor()
            
            # 获取总数
            cursor.execute("SELECT COUNT(*) FROM blacklist")
            total_count = cursor.fetchone()[0]
            
            # 获取今天新增数量
            cursor.execute("SELECT COUNT(*) FROM blacklist WHERE DATE(created_at) = CURRENT_DATE")
            today_count = cursor.fetchone()[0]
            
            cursor.close()
            
            return {
                'total': total_count,
                'today': today_count
            }
            
        except psycopg2.Error as e:
            logging.error(f"获取黑名单统计失败: {e}")
            return {'total': 0, 'today': 0}
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logging.info("数据库连接已关闭")

# 全局数据库实例
_blacklist_db = None

def get_blacklist_db():
    """获取黑名单数据库实例（单例模式）"""
    global _blacklist_db
    if _blacklist_db is None:
        _blacklist_db = BlacklistDB()
    return _blacklist_db

def add_buyer_to_blacklist(buyer_eid, order_no=None, buyer_nick=None, user_id=None, recharge_amount=None, notes=None):
    """添加买家到黑名单的便捷函数"""
    db = get_blacklist_db()
    return db.add_to_blacklist(buyer_eid, order_no, buyer_nick, user_id, recharge_amount, notes)

def check_buyer_in_blacklist(buyer_eid):
    """检查买家是否在黑名单中的便捷函数"""
    db = get_blacklist_db()
    return db.is_in_blacklist(buyer_eid)

def get_blacklist_statistics():
    """获取黑名单统计信息的便捷函数"""
    db = get_blacklist_db()
    return db.get_blacklist_stats()
