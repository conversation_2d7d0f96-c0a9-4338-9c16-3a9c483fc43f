#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
充值记录数据库功能测试脚本
"""

import sys
import logging
from datetime import datetime
from recharge_records_db import (
    add_recharge_record, 
    get_all_recharge_records, 
    get_recharge_records_by_order,
    delete_recharge_record,
    get_recharge_statistics
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    try:
        stats = get_recharge_statistics()
        print(f"✅ 数据库连接成功")
        print(f"   总记录数: {stats['total']}")
        print(f"   今日记录数: {stats['today']}")
        print(f"   成功记录数: {stats['success']}")
        print(f"   失败记录数: {stats['failed']}")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_add_record():
    """测试添加充值记录"""
    print("\n=== 测试添加充值记录 ===")
    
    test_records = [
        {
            'recharge_time': datetime.now(),
            'order_no': 'TEST001',
            'buyer_nick': '测试用户1',
            'user_id': '1001',
            'amount': 5.58,
            'recharge_type': '普通充值',
            'status': '成功',
            'failure_reason': None
        },
        {
            'recharge_time': datetime.now(),
            'order_no': 'TEST002',
            'buyer_nick': '测试用户2',
            'user_id': '1002',
            'amount': 4.50,
            'recharge_type': '强制充值',
            'status': '失败',
            'failure_reason': '用户不存在'
        },
        {
            'recharge_time': datetime.now(),
            'order_no': 'TEST003',
            'buyer_nick': '测试用户3',
            'user_id': '1003',
            'amount': 6.00,
            'recharge_type': '普通充值',
            'status': '拒绝',
            'failure_reason': '用户余额不为0，判定为老用户'
        }
    ]
    
    added_ids = []
    for record in test_records:
        try:
            record_id = add_recharge_record(**record)
            if record_id:
                print(f"✅ 成功添加记录: {record['order_no']} -> ID: {record_id}")
                added_ids.append(record_id)
            else:
                print(f"❌ 添加记录失败: {record['order_no']}")
        except Exception as e:
            print(f"❌ 添加记录异常: {record['order_no']} - {e}")
    
    return added_ids

def test_get_records():
    """测试获取充值记录"""
    print("\n=== 测试获取充值记录 ===")
    
    try:
        records = get_all_recharge_records(limit=10)
        print(f"✅ 成功获取 {len(records)} 条记录")
        
        for i, record in enumerate(records[:3], 1):  # 只显示前3条
            print(f"   记录 {i}:")
            print(f"     ID: {record['id']}")
            print(f"     时间: {record['recharge_time']}")
            print(f"     订单号: {record['order_no']}")
            print(f"     买家昵称: {record['buyer_nick']}")
            print(f"     用户ID: {record['user_id']}")
            print(f"     金额: {record['amount']}")
            print(f"     类型: {record['recharge_type']}")
            print(f"     状态: {record['status']}")
            if record['failure_reason']:
                print(f"     失败原因: {record['failure_reason']}")
            print()
        
        return records
    except Exception as e:
        print(f"❌ 获取记录失败: {e}")
        return []

def test_get_by_order():
    """测试根据订单号获取记录"""
    print("\n=== 测试根据订单号获取记录 ===")
    
    test_order_no = 'TEST001'
    try:
        records = get_recharge_records_by_order(test_order_no)
        print(f"✅ 订单号 {test_order_no} 的记录数: {len(records)}")
        
        for record in records:
            print(f"   ID: {record['id']}, 状态: {record['status']}, 金额: {record['amount']}")
        
        return records
    except Exception as e:
        print(f"❌ 根据订单号获取记录失败: {e}")
        return []

def test_delete_record(record_ids):
    """测试删除记录"""
    print("\n=== 测试删除记录 ===")
    
    if not record_ids:
        print("⚠️  没有可删除的记录ID")
        return
    
    # 删除第一个测试记录
    test_id = record_ids[0]
    try:
        deleted_record = delete_recharge_record(test_id)
        if deleted_record:
            print(f"✅ 成功删除记录: ID={test_id}, 订单号={deleted_record[0]}")
        else:
            print(f"❌ 删除记录失败: ID={test_id}")
    except Exception as e:
        print(f"❌ 删除记录异常: {e}")

def test_statistics():
    """测试统计功能"""
    print("\n=== 测试统计功能 ===")
    
    try:
        stats = get_recharge_statistics()
        print("✅ 统计信息:")
        print(f"   总记录数: {stats['total']}")
        print(f"   今日记录数: {stats['today']}")
        print(f"   成功记录数: {stats['success']}")
        print(f"   失败记录数: {stats['failed']}")
        
        if stats['total'] > 0:
            success_rate = (stats['success'] / stats['total']) * 100
            print(f"   成功率: {success_rate:.1f}%")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")

def cleanup_test_records():
    """清理测试记录"""
    print("\n=== 清理测试记录 ===")
    
    test_orders = ['TEST001', 'TEST002', 'TEST003']
    cleaned_count = 0
    
    for order_no in test_orders:
        try:
            records = get_recharge_records_by_order(order_no)
            for record in records:
                deleted = delete_recharge_record(record['id'])
                if deleted:
                    cleaned_count += 1
                    print(f"✅ 清理测试记录: {order_no}")
        except Exception as e:
            print(f"❌ 清理记录失败: {order_no} - {e}")
    
    print(f"✅ 共清理 {cleaned_count} 条测试记录")

def main():
    """主测试函数"""
    print("🚀 开始充值记录数据库功能测试")
    print("=" * 50)
    
    # 1. 测试数据库连接
    if not test_database_connection():
        print("❌ 数据库连接失败，终止测试")
        return False
    
    # 2. 测试添加记录
    added_ids = test_add_record()
    
    # 3. 测试获取记录
    all_records = test_get_records()
    
    # 4. 测试根据订单号获取记录
    order_records = test_get_by_order()
    
    # 5. 测试统计功能
    test_statistics()
    
    # 6. 测试删除记录
    test_delete_record(added_ids)
    
    # 7. 清理测试记录
    cleanup_test_records()
    
    print("\n" + "=" * 50)
    print("✅ 充值记录数据库功能测试完成！")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)
