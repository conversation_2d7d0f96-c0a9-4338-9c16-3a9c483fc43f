#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("开始测试...")

try:
    import psycopg2
    print("psycopg2 导入成功")
except ImportError as e:
    print(f"psycopg2 导入失败: {e}")
    exit(1)

try:
    import configparser
    print("configparser 导入成功")
except ImportError as e:
    print(f"configparser 导入失败: {e}")
    exit(1)

# 读取配置文件
try:
    config = configparser.ConfigParser()
    config.read('manual_recharge_config.ini', encoding='utf-8')
    print("配置文件读取成功")
    
    # 数据库配置
    DB_HOST = config['Database']['host']
    DB_PORT = config['Database']['port']
    DB_USERNAME = config['Database']['username']
    DB_PASSWORD = config['Database']['password']
    DB_NAME = config['Database']['database']
    
    print(f"数据库配置: {DB_HOST}:{DB_PORT}/{DB_NAME}")
    
except Exception as e:
    print(f"配置文件读取失败: {e}")
    exit(1)

# 测试数据库连接
try:
    print("尝试连接数据库...")
    connection = psycopg2.connect(
        host=DB_HOST,
        port=DB_PORT,
        user=DB_USERNAME,
        password=DB_PASSWORD,
        database=DB_NAME,
        connect_timeout=10
    )
    print("数据库连接成功")
    
    cursor = connection.cursor()
    
    # 检查表是否存在
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'recharge_records'
        );
    """)
    
    table_exists = cursor.fetchone()[0]
    print(f"充值记录表存在: {table_exists}")
    
    if table_exists:
        # 检查表结构
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'recharge_records'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        print("当前表结构:")
        for col in columns:
            print(f"  {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})")
        
        # 检查记录数
        cursor.execute("SELECT COUNT(*) FROM recharge_records;")
        count = cursor.fetchone()[0]
        print(f"总记录数: {count}")
        
        # 检查缺失的字段
        column_names = [col[0] for col in columns]
        required_fields = ['is_deducted', 'deducted_time', 'updated_at']
        missing_fields = [field for field in required_fields if field not in column_names]
        
        if missing_fields:
            print(f"缺失字段: {missing_fields}")
            
            # 添加缺失字段
            for field in missing_fields:
                if field == 'is_deducted':
                    print("添加 is_deducted 字段...")
                    cursor.execute("ALTER TABLE recharge_records ADD COLUMN is_deducted BOOLEAN DEFAULT FALSE;")
                    print("is_deducted 字段添加成功")
                elif field == 'deducted_time':
                    print("添加 deducted_time 字段...")
                    cursor.execute("ALTER TABLE recharge_records ADD COLUMN deducted_time TIMESTAMP;")
                    print("deducted_time 字段添加成功")
                elif field == 'updated_at':
                    print("添加 updated_at 字段...")
                    cursor.execute("ALTER TABLE recharge_records ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;")
                    print("updated_at 字段添加成功")
            
            connection.commit()
            print("所有缺失字段已添加")
        else:
            print("所有必需字段都已存在")
        
        # 测试完整查询
        print("测试完整查询...")
        cursor.execute("""
            SELECT id, recharge_time, order_no, buyer_nick, user_id,
                   amount, recharge_type, status, failure_reason, is_deducted, deducted_time, created_at
            FROM recharge_records
            ORDER BY recharge_time DESC
            LIMIT 5;
        """)
        
        records = cursor.fetchall()
        print(f"查询成功，返回 {len(records)} 条记录")
        
        if records:
            print("最新记录:")
            for i, record in enumerate(records):
                print(f"  记录 {i+1}: ID={record[0]}, 订单号={record[2]}, 用户ID={record[4]}, 金额={record[5]}, 状态={record[7]}")
    
    cursor.close()
    connection.close()
    print("数据库连接已关闭")
    print("测试完成！")
    
except psycopg2.Error as e:
    print(f"数据库操作失败: {e}")
except Exception as e:
    print(f"其他错误: {e}")
