#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from manual_recharge import check_user_eligibility, get_access_token
from blacklist_db import add_buyer_to_blacklist, check_buyer_in_blacklist

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_recharge_checks():
    """测试充值前的检查逻辑"""
    try:
        print("=== 测试充值前检查逻辑 ===")
        
        # 获取Token
        token = get_access_token()
        if not token:
            print("✗ 无法获取访问令牌")
            return False
        print("✓ 成功获取访问令牌")
        
        # 测试用例1：检查不存在的用户
        print("\n=== 测试用例1：不存在的用户 ===")
        is_eligible, reason = check_user_eligibility(999999, "test_buyer_eid_1", token)
        print(f"结果: {'通过' if is_eligible else '拒绝'}")
        print(f"原因: {reason}")
        
        # 测试用例2：添加一个测试buyer_eid到黑名单，然后检查
        print("\n=== 测试用例2：黑名单用户 ===")
        test_buyer_eid = "test_blacklist_buyer_456"
        add_buyer_to_blacklist(test_buyer_eid, "TEST_ORDER_BLACKLIST", "测试黑名单买家", "12345", 10.0, "测试黑名单")
        print(f"✓ 已添加 {test_buyer_eid} 到黑名单")
        
        # 检查黑名单用户
        is_eligible, reason = check_user_eligibility(12345, test_buyer_eid, token)
        print(f"结果: {'通过' if is_eligible else '拒绝'}")
        print(f"原因: {reason}")
        
        # 测试用例3：检查正常用户（如果存在的话）
        print("\n=== 测试用例3：尝试检查真实用户 ===")
        # 这里可以用一个真实的用户ID进行测试
        test_user_id = 1  # 假设用户ID为1
        is_eligible, reason = check_user_eligibility(test_user_id, "new_buyer_eid_789", token)
        print(f"结果: {'通过' if is_eligible else '拒绝'}")
        print(f"原因: {reason}")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        logging.error(f"测试充值检查失败: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = test_recharge_checks()
    if success:
        print("\n充值检查逻辑测试完成！")
    else:
        print("\n充值检查逻辑测试失败，请检查配置。")
