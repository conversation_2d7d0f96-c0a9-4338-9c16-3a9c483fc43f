#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import configparser

# 读取配置文件
config = configparser.ConfigParser()
config.read('manual_recharge_config.ini', encoding='utf-8')

# 数据库配置
DB_HOST = config['Database']['host']
DB_PORT = config['Database']['port']
DB_USERNAME = config['Database']['username']
DB_PASSWORD = config['Database']['password']
DB_NAME = config['Database']['database']

def check_database_structure():
    """检查数据库表结构"""
    try:
        # 连接数据库
        connection = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USERNAME,
            password=DB_PASSWORD,
            database=DB_NAME,
            connect_timeout=10
        )
        
        cursor = connection.cursor()
        
        print("=" * 60)
        print("数据库连接信息:")
        print(f"主机: {DB_HOST}:{DB_PORT}")
        print(f"数据库: {DB_NAME}")
        print(f"用户: {DB_USERNAME}")
        print("=" * 60)
        
        # 查看所有表
        print("\n1. 数据库中的所有表:")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
        """)
        tables = cursor.fetchall()
        for table in tables:
            print(f"  - {table[0]}")
        
        # 查看 recharge_records 表结构
        print("\n2. recharge_records 表结构:")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'recharge_records' 
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        print(f"{'列名':<20} {'数据类型':<20} {'可空':<10} {'默认值':<20}")
        print("-" * 70)
        for col in columns:
            column_name, data_type, is_nullable, column_default = col
            default_str = str(column_default) if column_default else ""
            print(f"{column_name:<20} {data_type:<20} {is_nullable:<10} {default_str:<20}")
        
        # 查看表的索引
        print("\n3. recharge_records 表的索引:")
        cursor.execute("""
            SELECT indexname, indexdef
            FROM pg_indexes 
            WHERE tablename = 'recharge_records';
        """)
        indexes = cursor.fetchall()
        for idx in indexes:
            print(f"  - {idx[0]}: {idx[1]}")
        
        # 查看表中的数据量
        print("\n4. 表数据统计:")
        cursor.execute("SELECT COUNT(*) FROM recharge_records;")
        total_count = cursor.fetchone()[0]
        print(f"  总记录数: {total_count}")
        
        if total_count > 0:
            # 查看最近的几条记录
            print("\n5. 最近的5条记录:")
            cursor.execute("""
                SELECT id, recharge_time, order_no, buyer_nick, user_id, amount, recharge_type, status
                FROM recharge_records 
                ORDER BY recharge_time DESC 
                LIMIT 5;
            """)
            recent_records = cursor.fetchall()
            
            print(f"{'ID':<5} {'时间':<20} {'订单号':<15} {'买家昵称':<12} {'用户ID':<8} {'金额':<8} {'类型':<8} {'状态':<8}")
            print("-" * 90)
            for record in recent_records:
                record_id, recharge_time, order_no, buyer_nick, user_id, amount, recharge_type, status = record
                time_str = recharge_time.strftime('%Y-%m-%d %H:%M:%S') if recharge_time else ""
                print(f"{record_id:<5} {time_str:<20} {order_no:<15} {buyer_nick:<12} {user_id:<8} {amount:<8} {recharge_type:<8} {status:<8}")
        
        # 检查是否存在问题字段
        print("\n6. 检查可能的问题:")
        
        # 检查是否有新字段
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'recharge_records' 
            AND column_name IN ('is_deducted', 'deducted_time', 'created_at', 'updated_at');
        """)
        new_fields = cursor.fetchall()
        
        expected_fields = ['is_deducted', 'deducted_time', 'created_at', 'updated_at']
        missing_fields = []
        existing_fields = [field[0] for field in new_fields]
        
        for field in expected_fields:
            if field not in existing_fields:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"  缺少字段: {', '.join(missing_fields)}")
        else:
            print("  所有预期字段都存在")
        
        # 检查数据类型是否正确
        cursor.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'recharge_records' 
            AND column_name IN ('amount', 'user_id', 'recharge_time');
        """)
        type_check = cursor.fetchall()
        
        print("\n  关键字段类型检查:")
        for col_name, data_type in type_check:
            print(f"    {col_name}: {data_type}")
        
        cursor.close()
        connection.close()
        
        print("\n" + "=" * 60)
        print("数据库结构检查完成")
        print("=" * 60)
        
    except psycopg2.Error as e:
        print(f"数据库连接或查询失败: {e}")
    except Exception as e:
        print(f"发生异常: {e}")

if __name__ == "__main__":
    check_database_structure()
