[Credentials]
app_id = 1040442267272517
app_secret = dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
send_msg_token = BE7C0BE750C6759628E892AFD609A141:3d2e784c0ea24c7aa9f563a8a7a621bd1fbec682f88444d18a933f807c24f052

[Login]
username = mywl
password = a63006320
tenant_id = 157

[Settings]
base_url = https://qq.py258.com
message_template = 
delivery_service_name = 龙马寄件
extra_charge = 1.2
min_order_amount = 4.0

[Redis]
host = **************
port = 6379
db = 0
password = 1104030777+.a

[Messages]
message_1 = 用户ID：{user_id} 已为您充值余额：{final_recharge_amount}元
message_2 = 首页寄快递---填寄、收地址---选快递---点击【下单】---选余额付
message_3 = 温馨提示： 1. 若超重请及时回龙马寄件补差价！ 2. 若超体积，按体积计费！

[UserType]
# 新用户判断标准：订单数量小于等于此值且余额为0
new_user_max_orders = 0
# 老用户判断标准：订单数量大于等于此值或余额不为0
old_user_min_orders = 3
# 老用户自动发送的消息内容
old_user_message = 您不是首单了，需要补1元。首单亏本的
