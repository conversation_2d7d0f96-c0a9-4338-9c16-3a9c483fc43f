#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import configparser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_recharge_table():
    """测试充值记录表"""
    
    # 读取配置文件
    config = configparser.ConfigParser()
    config.read('manual_recharge_config.ini', encoding='utf-8')
    
    # 数据库配置
    DB_HOST = config['Database']['host']
    DB_PORT = config['Database']['port']
    DB_USERNAME = config['Database']['username']
    DB_PASSWORD = config['Database']['password']
    DB_NAME = config['Database']['database']
    
    print(f"连接数据库: {DB_HOST}:{DB_PORT}/{DB_NAME}")
    
    try:
        # 连接数据库
        connection = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USERNAME,
            password=DB_PASSWORD,
            database=DB_NAME,
            connect_timeout=10
        )
        
        cursor = connection.cursor()
        
        # 1. 检查表是否存在
        print("\n1. 检查充值记录表是否存在...")
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'recharge_records'
            );
        """)
        
        table_exists = cursor.fetchone()[0]
        print(f"充值记录表存在: {table_exists}")
        
        if not table_exists:
            print("充值记录表不存在，需要创建表")
            return False
        
        # 2. 检查表结构
        print("\n2. 检查表结构...")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'recharge_records'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        print("表结构:")
        for col in columns:
            print(f"  {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})")
        
        # 3. 检查记录数
        print("\n3. 检查记录数...")
        cursor.execute("SELECT COUNT(*) FROM recharge_records;")
        count = cursor.fetchone()[0]
        print(f"总记录数: {count}")
        
        # 4. 查询最近的记录
        if count > 0:
            print("\n4. 查询最近的5条记录...")
            cursor.execute("""
                SELECT id, recharge_time, order_no, user_id, amount, status
                FROM recharge_records
                ORDER BY recharge_time DESC
                LIMIT 5;
            """)
            
            records = cursor.fetchall()
            for record in records:
                print(f"  ID: {record[0]}, 时间: {record[1]}, 订单: {record[2]}, 用户: {record[3]}, 金额: {record[4]}, 状态: {record[5]}")
        
        # 5. 检查缺失的字段并修复
        print("\n5. 检查并修复缺失的字段...")

        # 检查是否存在 is_deducted 字段
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'recharge_records'
                AND column_name = 'is_deducted'
            );
        """)
        has_is_deducted = cursor.fetchone()[0]
        print(f"is_deducted 字段存在: {has_is_deducted}")

        # 检查是否存在 deducted_time 字段
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'recharge_records'
                AND column_name = 'deducted_time'
            );
        """)
        has_deducted_time = cursor.fetchone()[0]
        print(f"deducted_time 字段存在: {has_deducted_time}")

        # 检查是否存在 updated_at 字段
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'recharge_records'
                AND column_name = 'updated_at'
            );
        """)
        has_updated_at = cursor.fetchone()[0]
        print(f"updated_at 字段存在: {has_updated_at}")

        # 添加缺失的字段
        fields_added = False
        if not has_is_deducted:
            print("添加 is_deducted 字段...")
            cursor.execute("ALTER TABLE recharge_records ADD COLUMN is_deducted BOOLEAN DEFAULT FALSE;")
            print("is_deducted 字段添加成功")
            fields_added = True

        if not has_deducted_time:
            print("添加 deducted_time 字段...")
            cursor.execute("ALTER TABLE recharge_records ADD COLUMN deducted_time TIMESTAMP;")
            print("deducted_time 字段添加成功")
            fields_added = True

        if not has_updated_at:
            print("添加 updated_at 字段...")
            cursor.execute("ALTER TABLE recharge_records ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;")
            print("updated_at 字段添加成功")
            fields_added = True

        if fields_added:
            print("字段修复完成！")
        else:
            print("所有必需字段都已存在")

        # 6. 测试完整的查询（模拟主程序中的查询）
        print("\n6. 测试完整查询...")
        try:
            cursor.execute("""
                SELECT id, recharge_time, order_no, buyer_nick, user_id,
                       amount, recharge_type, status, failure_reason, is_deducted, deducted_time, created_at
                FROM recharge_records
                ORDER BY recharge_time DESC
                LIMIT 10;
            """)

            full_records = cursor.fetchall()
            print(f"完整查询成功，返回 {len(full_records)} 条记录")

            if full_records:
                print("第一条记录详情:")
                record = full_records[0]
                print(f"  ID: {record[0]}")
                print(f"  时间: {record[1]}")
                print(f"  订单号: {record[2]}")
                print(f"  买家昵称: {record[3]}")
                print(f"  用户ID: {record[4]}")
                print(f"  金额: {record[5]}")
                print(f"  类型: {record[6]}")
                print(f"  状态: {record[7]}")
                print(f"  失败原因: {record[8] or '无'}")
                print(f"  已扣除: {record[9]}")
                print(f"  扣除时间: {record[10]}")
                print(f"  创建时间: {record[11]}")

        except Exception as e:
            print(f"完整查询失败: {e}")
            print("尝试使用简化查询...")

            cursor.execute("""
                SELECT id, recharge_time, order_no, buyer_nick, user_id,
                       amount, recharge_type, status, failure_reason, created_at
                FROM recharge_records
                ORDER BY recharge_time DESC
                LIMIT 10;
            """)

            simple_records = cursor.fetchall()
            print(f"简化查询成功，返回 {len(simple_records)} 条记录")
        
        cursor.close()
        connection.close()
        
        print("\n测试完成！")
        return True
        
    except psycopg2.Error as e:
        print(f"数据库操作失败: {e}")
        return False

if __name__ == "__main__":
    test_recharge_table()
