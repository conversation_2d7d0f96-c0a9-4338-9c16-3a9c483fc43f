#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import os
from datetime import datetime

# 配置日志，确保输出到文件
log_filename = f"test_recharge_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_logging_functionality():
    """测试日志功能"""
    print(f"=== 测试充值流程日志功能 ===")
    print(f"日志文件: {log_filename}")
    
    # 模拟充值流程的各个步骤
    logging.info("[充值流程] 开始普通充值检查 - 订单号: TEST_ORDER_001, 用户ID: 12345, 充值金额: 10.50元")
    logging.info("[充值流程] 步骤1: 获取访问令牌")
    logging.info("[充值流程] 步骤1成功: 获取到访问令牌")
    
    logging.info("[充值流程] 步骤2: 查找订单中的buyer_eid")
    logging.info("[充值流程] 步骤2成功: 找到订单信息, buyer_eid: test_buyer_eid_123")
    
    logging.info("[用户检查] 开始检查用户资格 - 用户ID: 12345, buyer_eid: test_buyer_eid_123")
    logging.info("[用户检查] 步骤1: 获取用户信息")
    logging.info("[用户检查] 步骤1成功: 获取到用户信息")
    logging.info("[用户检查] 步骤2: 检查用户余额 - 原始余额: 0, 转换后余额: 0.00元")
    logging.info("[用户检查] 步骤2通过: 用户余额为0")
    logging.info("[用户检查] 步骤3: 检查黑名单 - buyer_eid: test_buyer_eid_123")
    logging.info("[用户检查] 步骤3通过: 不在黑名单中")
    logging.info("[用户检查] 步骤4: 检查订单数限制 - 当前订单数: 0, 限制: 0")
    logging.info("[用户检查] 步骤4通过: 订单数符合要求")
    logging.info("[用户检查] 所有检查通过: 检查通过，符合新用户条件")
    
    logging.info("[充值流程] 步骤3: 用户资格检查通过，显示确认对话框")
    logging.info("[充值流程] 步骤4: 用户确认充值，开始执行充值操作")
    
    logging.info("[普通充值] 开始执行充值操作 - 订单号: TEST_ORDER_001, 用户ID: 12345, 充值金额: 10.50元")
    logging.info("[普通充值] 步骤1: 获取买家昵称和订单信息")
    logging.info("[普通充值] 步骤1: 使用传入的买家昵称: 测试买家")
    logging.info("[普通充值] 步骤1: 获取到订单信息, buyer_eid: test_buyer_eid_123")
    logging.info("[普通充值] 步骤2: 获取访问令牌")
    logging.info("[普通充值] 步骤2成功: 获取到访问令牌")
    
    logging.info("[充值API] 开始调用充值API - 用户ID: 12345, 金额: 10.50元, 订单号: TEST_ORDER_001")
    logging.info("[充值API] 请求URL: http://example.com/admin-api/kuaidi/mbr/user/recharge")
    logging.info("[充值API] 请求数据: {'mbrId': 12345, 'balanceType': '1', 'balance': '10.50', 'changeReason': '1', 'bizType': 'balanceRecharge', 'remark': 'TEST_ORDER_001'}")
    logging.info("[充值API] 发送充值请求...")
    logging.info("[充值API] 响应状态码: 200")
    logging.info("[充值API] 响应内容: {'code': 0, 'message': 'success'}")
    
    logging.info("[普通充值] 步骤3结果: 成功, 消息: 充值成功")
    logging.info("[普通充值] 步骤4: 充值成功，开始后续处理")
    logging.info("[普通充值] 步骤4a: 保存buyer_eid到黑名单数据库")
    logging.info("[普通充值] 步骤4a成功: buyer_eid test_buyer_eid_123 已保存到黑名单数据库")
    logging.info("[普通充值] 步骤4b: 发送消息通知")
    logging.info("[普通充值] 步骤4b成功: 消息通知发送完成")
    logging.info("[普通充值] 充值流程完成")
    
    print(f"\n✅ 日志测试完成！")
    print(f"📁 日志文件已保存到: {os.path.abspath(log_filename)}")
    
    # 显示日志文件内容
    if os.path.exists(log_filename):
        print(f"\n📄 日志文件内容预览:")
        print("-" * 80)
        with open(log_filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[-10:], 1):  # 显示最后10行
                print(f"{len(lines)-10+i:2d}: {line.rstrip()}")
        print("-" * 80)
        print(f"总共 {len(lines)} 行日志")
    
    return log_filename

if __name__ == "__main__":
    log_file = test_logging_functionality()
    print(f"\n现在您可以进行实际的充值操作，所有详细日志都会保存到:")
    print(f"1. manual_recharge.log (主日志文件)")
    print(f"2. {log_file} (测试日志文件)")
    print(f"\n充值流程中的每个步骤都会有详细的日志记录，包括:")
    print("- 用户资格检查过程")
    print("- API调用详情")
    print("- 黑名单保存操作")
    print("- 错误和异常信息")
