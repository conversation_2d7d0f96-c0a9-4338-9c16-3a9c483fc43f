#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from manual_recharge import get_all_orders, get_access_token

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def verify_buyer_eid_in_orders():
    """验证拉取订单时buyer_eid是否正确保存"""
    print("=== 验证订单拉取时buyer_eid的保存情况 ===")
    
    try:
        # 获取Token
        token = get_access_token()
        if not token:
            print("❌ 无法获取访问令牌")
            return False
        print("✅ 成功获取访问令牌")
        
        # 清除缓存，强制重新获取
        import manual_recharge
        manual_recharge._orders_cache = {}
        print("✅ 已清除订单缓存")
        
        # 获取订单列表
        print("\n📥 正在拉取订单列表...")
        orders = get_all_orders(token, days=7)
        if not orders:
            print("❌ 无法获取订单列表")
            return False
        
        print(f"✅ 成功获取 {len(orders)} 个订单")
        
        # 详细检查每个订单的buyer_eid
        print(f"\n🔍 详细检查所有订单的buyer_eid字段:")
        print("-" * 100)
        print(f"{'序号':<4} {'订单号':<20} {'买家昵称':<15} {'金额':<8} {'buyer_eid':<30} {'状态'}")
        print("-" * 100)
        
        orders_with_buyer_eid = 0
        orders_without_buyer_eid = 0
        target_orders = ["2878089314222615854", "4683940419366546746"]  # 从日志中看到的订单号
        found_target_orders = {}
        
        for i, order in enumerate(orders, 1):
            order_no = order.get('order_no', 'N/A')
            buyer_eid = order.get('buyer_eid', '')
            buyer_nick = order.get('buyer_nick', 'N/A')
            pay_amount = order.get('pay_amount_yuan', 0)
            
            # 检查buyer_eid状态
            if buyer_eid and buyer_eid.strip():
                orders_with_buyer_eid += 1
                status = "✅ 有"
            else:
                orders_without_buyer_eid += 1
                status = "❌ 无"
            
            # 显示前20个订单的详细信息
            if i <= 20:
                buyer_eid_display = buyer_eid[:25] + "..." if len(buyer_eid) > 25 else buyer_eid
                print(f"{i:<4} {order_no:<20} {buyer_nick:<15} {pay_amount:<8} {buyer_eid_display:<30} {status}")
            
            # 检查目标订单
            if order_no in target_orders:
                found_target_orders[order_no] = {
                    'buyer_eid': buyer_eid,
                    'buyer_nick': buyer_nick,
                    'pay_amount': pay_amount,
                    'has_buyer_eid': bool(buyer_eid and buyer_eid.strip())
                }
        
        if len(orders) > 20:
            print(f"... (还有 {len(orders) - 20} 个订单)")
        
        print("-" * 100)
        
        # 统计结果
        print(f"\n📊 统计结果:")
        print(f"总订单数: {len(orders)}")
        print(f"有buyer_eid的订单: {orders_with_buyer_eid}")
        print(f"缺少buyer_eid的订单: {orders_without_buyer_eid}")
        print(f"buyer_eid覆盖率: {orders_with_buyer_eid/len(orders)*100:.1f}%")
        
        # 检查目标订单
        print(f"\n🎯 目标订单检查:")
        for order_no in target_orders:
            if order_no in found_target_orders:
                order_info = found_target_orders[order_no]
                print(f"订单号: {order_no}")
                print(f"  买家昵称: {order_info['buyer_nick']}")
                print(f"  金额: {order_info['pay_amount']}元")
                print(f"  buyer_eid: {order_info['buyer_eid']}")
                print(f"  状态: {'✅ 找到且有buyer_eid' if order_info['has_buyer_eid'] else '❌ 找到但缺少buyer_eid'}")
            else:
                print(f"订单号: {order_no} - ❌ 未找到")
            print()
        
        # 检查订单数据结构
        if orders:
            first_order = orders[0]
            print(f"📋 第一个订单的完整字段:")
            for key, value in first_order.items():
                if key == 'buyer_eid':
                    print(f"  🔑 {key}: {value} {'✅' if value else '❌'}")
                else:
                    print(f"     {key}: {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
        
        return orders_with_buyer_eid > 0
        
    except Exception as e:
        print(f"❌ 验证异常: {e}")
        logging.error(f"验证buyer_eid保存异常: {e}", exc_info=True)
        return False

def check_specific_order_in_cache(order_no):
    """检查特定订单是否在缓存中"""
    print(f"\n🔍 检查订单 {order_no} 是否在缓存中:")
    
    try:
        import manual_recharge
        cached_orders = manual_recharge._orders_cache
        
        if not cached_orders:
            print("❌ 缓存为空")
            return False
        
        print(f"缓存中有 {len(cached_orders)} 个订单")
        
        for order in cached_orders:
            if order.get('order_no') == order_no:
                buyer_eid = order.get('buyer_eid', '')
                buyer_nick = order.get('buyer_nick', 'N/A')
                pay_amount = order.get('pay_amount_yuan', 0)
                
                print(f"✅ 在缓存中找到订单:")
                print(f"  订单号: {order_no}")
                print(f"  买家昵称: {buyer_nick}")
                print(f"  金额: {pay_amount}元")
                print(f"  buyer_eid: {buyer_eid}")
                print(f"  buyer_eid状态: {'✅ 存在' if buyer_eid and buyer_eid.strip() else '❌ 缺失'}")
                return True
        
        print(f"❌ 在缓存中未找到订单 {order_no}")
        
        # 显示缓存中的前5个订单号
        print("缓存中的前5个订单号:")
        for i, order in enumerate(cached_orders[:5], 1):
            print(f"  {i}. {order.get('order_no', 'N/A')}")
        
        return False
        
    except Exception as e:
        print(f"❌ 检查缓存异常: {e}")
        return False

if __name__ == "__main__":
    print("开始验证订单拉取时buyer_eid的保存情况...")
    
    # 验证buyer_eid保存
    success = verify_buyer_eid_in_orders()
    
    # 检查特定订单在缓存中的情况
    target_order = "2878089314222615854"  # 从最新日志中看到的订单号
    in_cache = check_specific_order_in_cache(target_order)
    
    print(f"\n{'='*60}")
    print("验证结果总结:")
    print(f"buyer_eid保存验证: {'✅ 通过' if success else '❌ 失败'}")
    print(f"目标订单在缓存中: {'✅ 存在' if in_cache else '❌ 不存在'}")
    
    if success and not in_cache:
        print("\n⚠️ 问题分析:")
        print("1. 订单拉取时buyer_eid保存正常")
        print("2. 但目标订单不在缓存中")
        print("3. 可能是订单过滤逻辑有问题，或者订单不满足过滤条件")
        print("4. 建议检查订单过滤条件（金额、状态等）")
    elif not success:
        print("\n⚠️ 问题分析:")
        print("1. 订单拉取时buyer_eid保存有问题")
        print("2. 需要检查API返回的数据结构")
        print("3. 可能是API字段变化或数据格式问题")
    else:
        print("\n🎉 一切正常!")
        print("buyer_eid保存和缓存都正常工作")
