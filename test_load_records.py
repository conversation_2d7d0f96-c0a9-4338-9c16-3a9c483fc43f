#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试充值记录加载功能
"""

import tkinter as tk
from tkinter import ttk
import logging
from recharge_records_db import get_all_recharge_records

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_load_records():
    """测试加载充值记录到界面"""
    print("🚀 测试充值记录加载功能")
    print("=" * 40)
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("充值记录加载测试")
    root.geometry("800x400")
    
    # 创建表格
    columns = ('db_id', 'time', 'order_no', 'buyer_nick', 'user_id', 'amount', 'type', 'status', 'reason', 'action')
    tree = ttk.Treeview(root, columns=columns, show='headings', height=15)
    
    # 设置列标题
    tree.heading('db_id', text='')  # 隐藏的数据库ID列
    tree.heading('time', text='充值时间')
    tree.heading('order_no', text='订单号')
    tree.heading('buyer_nick', text='买家昵称')
    tree.heading('user_id', text='用户ID')
    tree.heading('amount', text='充值金额(元)')
    tree.heading('type', text='充值类型')
    tree.heading('status', text='状态')
    tree.heading('reason', text='失败原因')
    tree.heading('action', text='操作')
    
    # 设置列宽
    tree.column('db_id', width=0, minwidth=0)  # 隐藏
    tree.column('time', width=120, anchor='center')
    tree.column('order_no', width=120, anchor='w')
    tree.column('buyer_nick', width=100, anchor='w')
    tree.column('user_id', width=80, anchor='center')
    tree.column('amount', width=90, anchor='center')
    tree.column('type', width=80, anchor='center')
    tree.column('status', width=60, anchor='center')
    tree.column('reason', width=150, anchor='w')
    tree.column('action', width=60, anchor='center')
    
    # 配置状态标签颜色
    tree.tag_configure('success', foreground='green')
    tree.tag_configure('failed', foreground='red')
    tree.tag_configure('warning', foreground='orange')
    tree.tag_configure('normal', foreground='black')
    
    # 添加滚动条
    scrollbar = ttk.Scrollbar(root, orient='vertical', command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)
    
    # 布局
    tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
    scrollbar.pack(side='right', fill='y', pady=5)
    
    # 从数据库加载记录
    try:
        print("正在从数据库加载记录...")
        db_records = get_all_recharge_records(limit=50)
        print(f"从数据库获取到 {len(db_records)} 条记录")
        
        # 清空现有记录
        for item in tree.get_children():
            tree.delete(item)
        
        # 添加记录到表格
        for i, db_record in enumerate(db_records):
            # 确定状态标签
            status = db_record['status']
            if status == "成功":
                status_tag = 'success'
            elif status in ["失败", "异常"]:
                status_tag = 'failed'
            elif status == "拒绝":
                status_tag = 'warning'
            else:
                status_tag = 'normal'
            
            # 插入到表格
            item = tree.insert('', 'end', values=(
                db_record['id'],  # 数据库ID（隐藏列）
                db_record['recharge_time'].strftime('%Y-%m-%d %H:%M:%S'),
                db_record['order_no'],
                db_record['buyer_nick'],
                db_record['user_id'],
                f"{db_record['amount']:.2f}",
                db_record['recharge_type'],
                db_record['status'],
                db_record['failure_reason'] or '',
                "扣除" if status == "成功" else "-"
            ), tags=(status_tag,))
            
            print(f"  {i+1}. {db_record['order_no']} - {db_record['status']} - {db_record['amount']}元")
        
        print(f"✅ 成功加载 {len(db_records)} 条记录到界面")
        
        # 添加状态标签
        status_label = tk.Label(root, text=f"已加载 {len(db_records)} 条充值记录", 
                               bg='lightgreen', fg='black', font=('Arial', 10, 'bold'))
        status_label.pack(side='bottom', fill='x', padx=5, pady=5)
        
        # 显示窗口
        print("显示测试窗口，请检查记录是否正确显示...")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 加载记录失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_load_records()
