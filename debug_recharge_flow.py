#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import os
from datetime import datetime

def analyze_recharge_flow():
    """分析充值流程，找出问题所在"""
    print("=== 分析充值流程 ===")
    
    # 1. 检查日志中的充值相关信息
    log_files = ['manual_recharge.log', 'manual_recharge.log.1']
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n检查日志文件: {log_file}")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 查找充值相关的关键词
                recharge_keywords = [
                    '手动充值成功',
                    '充值成功',
                    '充值失败', 
                    '保存buyer_eid',
                    '添加到黑名单',
                    '[充值流程]',
                    '[强制充值流程]',
                    '[普通充值]',
                    '[强制充值]',
                    'do_recharge',
                    '_do_manual_recharge'
                ]
                
                found_lines = []
                for line_num, line in enumerate(lines, 1):
                    for keyword in recharge_keywords:
                        if keyword in line:
                            found_lines.append((line_num, line.strip()))
                            break
                
                if found_lines:
                    print(f"找到 {len(found_lines)} 条充值相关日志:")
                    for line_num, line in found_lines[-20:]:  # 显示最后20条
                        print(f"  行{line_num}: {line}")
                else:
                    print("未找到充值相关日志")
                    
            except Exception as e:
                print(f"读取日志文件失败: {e}")
        else:
            print(f"日志文件 {log_file} 不存在")

def check_recharge_methods():
    """检查manual_recharge.py中的充值方法"""
    print(f"\n=== 检查充值方法 ===")
    
    try:
        with open('manual_recharge.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找充值相关的方法定义
        methods_to_check = [
            'def do_recharge',
            'def _do_manual_recharge', 
            'def execute_normal_recharge',
            'def execute_force_recharge',
            'def manual_recharge_user'
        ]
        
        for method in methods_to_check:
            if method in content:
                print(f"✓ 找到方法: {method}")
                # 查找该方法中是否有保存黑名单的代码
                method_start = content.find(method)
                if method_start != -1:
                    # 找到下一个方法定义或文件结尾
                    next_method = content.find('\ndef ', method_start + 1)
                    if next_method == -1:
                        method_content = content[method_start:]
                    else:
                        method_content = content[method_start:next_method]
                    
                    if 'add_buyer_to_blacklist' in method_content:
                        print(f"  ✓ {method} 包含黑名单保存代码")
                    else:
                        print(f"  ❌ {method} 不包含黑名单保存代码")
            else:
                print(f"❌ 未找到方法: {method}")
        
        # 检查按钮绑定
        print(f"\n=== 检查按钮绑定 ===")
        button_bindings = [
            'execute_normal_recharge',
            'execute_force_recharge',
            '_do_manual_recharge'
        ]
        
        for binding in button_bindings:
            if binding in content:
                print(f"✓ 找到按钮绑定: {binding}")
            else:
                print(f"❌ 未找到按钮绑定: {binding}")
                
    except Exception as e:
        print(f"检查充值方法失败: {e}")

def suggest_solutions():
    """建议解决方案"""
    print(f"\n=== 建议解决方案 ===")
    print("基于分析结果，可能的问题和解决方案:")
    print()
    print("1. **如果使用的是旧的充值流程**:")
    print("   - 需要在旧流程中也添加黑名单保存代码")
    print("   - 或者修改按钮绑定，使用新的充值流程")
    print()
    print("2. **如果充值过程中出现异常**:")
    print("   - 检查日志中的错误信息")
    print("   - 确保数据库连接正常")
    print("   - 确保订单数据包含buyer_eid字段")
    print()
    print("3. **如果充值成功但没有保存黑名单**:")
    print("   - 检查保存黑名单的代码是否被执行")
    print("   - 检查异常处理是否吞掉了错误")
    print()
    print("4. **测试建议**:")
    print("   - 进行一次完整的充值操作")
    print("   - 观察日志输出，确认执行路径")
    print("   - 检查数据库中是否有新记录")

if __name__ == "__main__":
    print("开始分析充值流程问题...")
    
    analyze_recharge_flow()
    check_recharge_methods()
    suggest_solutions()
    
    print(f"\n{'='*60}")
    print("分析完成！请根据上述信息进行问题排查。")
    print("如果需要进一步调试，建议:")
    print("1. 进行一次实际充值操作")
    print("2. 实时查看日志: python view_recharge_logs.py")
    print("3. 检查数据库记录: python query_blacklist.py")
