#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试主程序与数据库的集成功能
"""

import sys
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_database_integration():
    """测试数据库集成功能"""
    print("🚀 开始测试主程序数据库集成功能")
    print("=" * 50)
    
    try:
        # 导入数据库模块
        from recharge_records_db import (
            add_recharge_record, 
            get_all_recharge_records, 
            get_recharge_records_by_order,
            delete_recharge_record,
            get_recharge_statistics
        )
        
        print("✅ 成功导入数据库模块")
        
        # 测试添加记录
        print("\n=== 测试添加充值记录 ===")
        
        # 添加成功记录
        record_id1 = add_recharge_record(
            recharge_time=datetime.now(),
            order_no='INTEGRATION_TEST001',
            buyer_nick='集成测试用户1',
            user_id='3001',
            amount=8.88,
            recharge_type='普通充值',
            status='成功',
            failure_reason=None
        )
        
        if record_id1:
            print(f"✅ 成功添加成功记录: ID={record_id1}")
        else:
            print("❌ 添加成功记录失败")
            return False
        
        # 添加失败记录
        record_id2 = add_recharge_record(
            recharge_time=datetime.now(),
            order_no='INTEGRATION_TEST002',
            buyer_nick='集成测试用户2',
            user_id='3002',
            amount=6.66,
            recharge_type='强制充值',
            status='失败',
            failure_reason='充值API失败: 用户不存在'
        )
        
        if record_id2:
            print(f"✅ 成功添加失败记录: ID={record_id2}")
        else:
            print("❌ 添加失败记录失败")
            return False
        
        # 添加拒绝记录
        record_id3 = add_recharge_record(
            recharge_time=datetime.now(),
            order_no='INTEGRATION_TEST003',
            buyer_nick='集成测试用户3',
            user_id='3003',
            amount=4.44,
            recharge_type='普通充值',
            status='拒绝',
            failure_reason='用户余额不为0，判定为老用户'
        )
        
        if record_id3:
            print(f"✅ 成功添加拒绝记录: ID={record_id3}")
        else:
            print("❌ 添加拒绝记录失败")
            return False
        
        # 测试查询记录
        print("\n=== 测试查询记录 ===")
        
        # 查询所有记录
        all_records = get_all_recharge_records(limit=10)
        print(f"✅ 查询到 {len(all_records)} 条记录")
        
        # 显示最新的3条记录
        for i, record in enumerate(all_records[:3], 1):
            print(f"   记录 {i}: {record['order_no']} - {record['status']} - {record['amount']}元")
        
        # 按订单号查询
        test_records = get_recharge_records_by_order('INTEGRATION_TEST001')
        print(f"✅ 订单号查询结果: {len(test_records)} 条记录")
        
        # 测试统计功能
        print("\n=== 测试统计功能 ===")
        stats = get_recharge_statistics()
        print(f"✅ 统计信息:")
        print(f"   总记录数: {stats['total']}")
        print(f"   今日记录数: {stats['today']}")
        print(f"   成功记录数: {stats['success']}")
        print(f"   失败记录数: {stats['failed']}")
        
        # 测试删除记录
        print("\n=== 测试删除记录 ===")
        
        # 删除第一个测试记录
        deleted_record = delete_recharge_record(record_id1)
        if deleted_record:
            print(f"✅ 成功删除记录: ID={record_id1}, 订单号={deleted_record[0]}")
        else:
            print(f"❌ 删除记录失败: ID={record_id1}")
        
        # 清理其他测试记录
        print("\n=== 清理测试记录 ===")
        cleanup_count = 0
        
        for order_no in ['INTEGRATION_TEST002', 'INTEGRATION_TEST003']:
            records = get_recharge_records_by_order(order_no)
            for record in records:
                deleted = delete_recharge_record(record['id'])
                if deleted:
                    cleanup_count += 1
                    print(f"✅ 清理记录: {order_no}")
        
        print(f"✅ 共清理 {cleanup_count} 条测试记录")
        
        print("\n" + "=" * 50)
        print("✅ 主程序数据库集成测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理机制"""
    print("\n🛡️ 测试错误处理机制")
    print("=" * 30)
    
    try:
        from recharge_records_db import add_recharge_record
        
        # 测试无效数据
        print("=== 测试无效数据处理 ===")
        
        # 测试空订单号
        try:
            result = add_recharge_record(
                recharge_time=datetime.now(),
                order_no='',  # 空订单号
                buyer_nick='测试用户',
                user_id='4001',
                amount=1.00,
                recharge_type='普通充值',
                status='成功'
            )
            print(f"⚠️  空订单号处理结果: {result}")
        except Exception as e:
            print(f"✅ 空订单号正确抛出异常: {e}")
        
        # 测试无效金额
        try:
            result = add_recharge_record(
                recharge_time=datetime.now(),
                order_no='ERROR_TEST001',
                buyer_nick='测试用户',
                user_id='4002',
                amount=-1.00,  # 负数金额
                recharge_type='普通充值',
                status='成功'
            )
            print(f"⚠️  负数金额处理结果: {result}")
        except Exception as e:
            print(f"✅ 负数金额正确抛出异常: {e}")
        
        print("✅ 错误处理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    success1 = test_database_integration()
    success2 = test_error_handling()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        return True
    else:
        print("\n❌ 部分测试失败！")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)
