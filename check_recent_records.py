#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import configparser
from datetime import datetime, timedelta
import os

# 读取配置文件
config = configparser.ConfigParser()
config.read('manual_recharge_config.ini', encoding='utf-8')

# 数据库配置
DB_HOST = config['Database']['host']
DB_PORT = config['Database']['port']
DB_USERNAME = config['Database']['username']
DB_PASSWORD = config['Database']['password']
DB_NAME = config['Database']['database']

def check_recent_records():
    """检查最近的记录"""
    try:
        connection = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USERNAME,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        
        cursor = connection.cursor()
        
        # 查询最近1小时的记录
        one_hour_ago = datetime.now() - timedelta(hours=1)
        
        query = """
        SELECT id, buyer_eid, order_no, buyer_nick, user_id, recharge_amount, 
               created_at, notes
        FROM blacklist 
        WHERE created_at >= %s
        ORDER BY created_at DESC
        """
        
        cursor.execute(query, (one_hour_ago,))
        recent_records = cursor.fetchall()
        
        print("=== 最近1小时的充值记录 ===")
        if recent_records:
            for record in recent_records:
                id_val, buyer_eid, order_no, buyer_nick, user_id, recharge_amount, created_at, notes = record
                print(f"ID: {id_val}")
                print(f"buyer_eid: {buyer_eid}")
                print(f"订单号: {order_no}")
                print(f"买家昵称: {buyer_nick}")
                print(f"用户ID: {user_id}")
                print(f"充值金额: {recharge_amount:.2f}元")
                print(f"创建时间: {created_at}")
                print(f"备注: {notes}")
                print("-" * 50)
        else:
            print("最近1小时内没有新的充值记录")
        
        # 查询最新的5条记录
        print("\n=== 最新的5条记录 ===")
        latest_query = """
        SELECT id, buyer_eid, order_no, buyer_nick, user_id, recharge_amount, 
               created_at, notes
        FROM blacklist 
        ORDER BY created_at DESC
        LIMIT 5
        """
        
        cursor.execute(latest_query)
        latest_records = cursor.fetchall()
        
        for i, record in enumerate(latest_records, 1):
            id_val, buyer_eid, order_no, buyer_nick, user_id, recharge_amount, created_at, notes = record
            print(f"\n第{i}条记录:")
            print(f"  ID: {id_val}")
            print(f"  buyer_eid: {buyer_eid}")
            print(f"  订单号: {order_no}")
            print(f"  买家昵称: {buyer_nick}")
            print(f"  用户ID: {user_id}")
            print(f"  充值金额: {recharge_amount:.2f}元")
            print(f"  创建时间: {created_at}")
            print(f"  备注: {notes}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"查询失败: {e}")

def check_log_files():
    """检查日志文件中的相关信息"""
    print("\n=== 检查日志文件 ===")
    
    log_files = ['manual_recharge.log', 'manual_recharge.log.1']
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n检查日志文件: {log_file}")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                # 查找最近的充值相关日志
                recent_lines = []
                for line in lines[-100:]:  # 检查最后100行
                    if any(keyword in line for keyword in ['手动充值成功', '添加到黑名单', 'buyer_eid', '充值成功']):
                        recent_lines.append(line.strip())
                
                if recent_lines:
                    print("最近的充值相关日志:")
                    for line in recent_lines[-10:]:  # 显示最后10条相关日志
                        print(f"  {line}")
                else:
                    print("未找到最近的充值相关日志")
                    
            except Exception as e:
                print(f"读取日志文件失败: {e}")
        else:
            print(f"日志文件 {log_file} 不存在")

if __name__ == "__main__":
    check_recent_records()
    check_log_files()
    
    print("\n" + "="*60)
    print("如果您刚才进行了充值但没有看到记录，可能的原因：")
    print("1. 充值过程中出现了异常")
    print("2. 订单数据中缺少buyer_eid字段")
    print("3. 数据库保存过程失败")
    print("4. 请检查上面的日志信息获取更多详情")
