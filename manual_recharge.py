import requests
import time
import hashlib
import json
import logging
import configparser
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from queue import Queue
from logging.handlers import RotatingFileHandler
from datetime import datetime
from blacklist_db import add_buyer_to_blacklist, check_buyer_in_blacklist
from recharge_records_db import add_recharge_record as db_add_recharge_record, get_all_recharge_records

# ========== 读取配置文件 ==========
config = configparser.ConfigParser()
config.read('manual_recharge_config.ini', encoding='utf-8')

# ========== 从配置文件读取各种参数 ==========
APP_ID = config['Credentials']['app_id']
APP_SECRET = config['Credentials']['app_secret']
SEND_MSG_TOKEN = config['Credentials']['send_msg_token']

# 登录配置
USERNAME = config['Login']['username']
PASSWORD = config['Login']['password']
TENANT_ID = config['Login']['tenant_id']

# 基础URL和消息模板
BASE_URL = config['Settings']['BASE_URL']
ORDER_BASE_URL = 'https://open.goofish.pro'

# 加价金额，默认值可以从配置文件中读取
EXTRA_CHARGE = float(config['Settings'].get('extra_charge', '1.08'))

# 订单金额最低要求，从配置文件读取
MIN_ORDER_AMOUNT = float(config['Settings'].get('min_order_amount', '4.2'))

# 从配置文件读取用户类型判断配置
NEW_USER_MAX_ORDERS = int(config.get('UserType', 'new_user_max_orders', fallback='0'))
OLD_USER_MIN_ORDERS = int(config.get('UserType', 'old_user_min_orders', fallback='3'))
OLD_USER_MESSAGE = config.get('UserType', 'old_user_message', fallback='您不是首单了，需要补1元。首单亏本的')

# 从配置文件读取性能优化配置
AUTO_REFRESH_INTERVAL = int(config.get('Performance', 'auto_refresh_interval', fallback='10'))
MIN_REFRESH_INTERVAL = int(config.get('Performance', 'min_refresh_interval', fallback='5'))
MAX_USER_QUERIES = int(config.get('Performance', 'max_user_queries', fallback='5'))
REQUEST_TIMEOUT = int(config.get('Performance', 'request_timeout', fallback='3'))
REQUEST_RETRIES = int(config.get('Performance', 'request_retries', fallback='1'))
CACHE_EXPIRE_TIME = int(config.get('Performance', 'cache_expire_time', fallback='600'))
ORDERS_CACHE_EXPIRE = int(config.get('Performance', 'orders_cache_expire', fallback='30'))

# ========== 日志配置 ==========
logger = logging.getLogger()
logger.setLevel(logging.DEBUG)

formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler = RotatingFileHandler('manual_recharge.log', maxBytes=5*1024*1024, backupCount=5, encoding='utf-8')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

# ========== 全局令牌缓存 ==========
_cached_token = None
_token_expire_time = None
_user_cache = {}  # 用户信息缓存 {mobile: user_info}
_cache_expire_time = CACHE_EXPIRE_TIME  # 使用配置文件中的缓存时间
_orders_cache = {}  # 订单缓存
_orders_cache_time = 0  # 订单缓存时间
_orders_cache_expire = ORDERS_CACHE_EXPIRE  # 使用配置文件中的订单缓存时间

# ========== 工具函数 ==========
def md5(text):
    m = hashlib.md5()
    m.update(text.encode("utf8"))
    return m.hexdigest()

def calculate_recharge_amount(pay_amount):
    """
    计算充值金额 = 实付金额 + 加价金额

    参数:
    - pay_amount: 实付金额

    返回:
    - 计算后的充值金额
    """
    try:
        pay_amount = float(pay_amount)
        recharge_amount = pay_amount + EXTRA_CHARGE
        return round(recharge_amount, 2)  # 保留两位小数
    except (ValueError, TypeError):
        return 0.0

def gen_sign(body_json, timestamp, app_id, app_secret):
    """
    生成签名
    """
    body_md5 = md5(body_json)
    sign_str = f"{app_id},{body_md5},{timestamp},{app_secret}"
    logging.debug(f"生成的签名字符串: {sign_str}")
    return md5(sign_str)

def save_to_file(data, status):
    """
    保存订单信息到文件
    """
    formatted_string = (
        f"订单号----{data['order_no']}----实付金额:{data['pay_amount']}元"
        f"----手机号:{data['mobile']}----会员名:{data['buyer_nick']}----最终充值金额:{data['final_recharge_amount']}----状态:{status}"
    )
    with open("manual_recharge_records.txt", "a", encoding='utf-8') as file:
        file.write(formatted_string + "\n")

def stable_request(method, url, **kwargs):
    """
    带有重试与超时机制的请求函数 - 优化版本，减少CPU负载
    """
    retries = kwargs.pop('retries', REQUEST_RETRIES)  # 使用配置文件中的重试次数
    backoff_factor = kwargs.pop('backoff_factor', 1.0)  # 减少等待时间
    timeout = kwargs.pop('timeout', REQUEST_TIMEOUT)  # 使用配置文件中的超时时间
    for attempt in range(retries):
        try:
            response = requests.request(method, url, timeout=timeout, **kwargs)
            if response.status_code == 401:
                return response
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            if attempt < retries - 1:  # 只在非最后一次重试时等待
                logging.warning(f"请求失败，第{attempt+1}次重试，URL: {url}, 错误: {e}")
                time.sleep(backoff_factor ** attempt)
    logging.error(f"请求失败，已重试{retries}次仍不成功, URL: {url}")
    return None

def get_access_token(force_refresh=False):
    """
    获取访问令牌，支持缓存和按需刷新
    """
    global _cached_token, _token_expire_time

    # 检查是否有有效的缓存令牌
    if not force_refresh and _cached_token and _token_expire_time:
        if time.time() < _token_expire_time:
            logging.debug(f"使用缓存的Token: {_cached_token[:20]}...")
            return _cached_token

    # 获取新令牌
    url = f"{BASE_URL}/admin-api/system/auth/login"
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'Tenant-Id': TENANT_ID
    }
    data = json.dumps({"username": USERNAME, "password": PASSWORD})
    response = stable_request('post', url, headers=headers, data=data)

    if response and response.status_code == 200:
        try:
            response_data = response.json()
            token = response_data['data']['accessToken']

            # 缓存令牌，设置过期时间（默认1小时）
            _cached_token = token
            _token_expire_time = time.time() + 3600  # 1小时后过期

            logging.info(f"登录成功，获取到新Token: {token[:20]}...")
            return token
        except (KeyError, TypeError) as e:
            logging.error(f"解析Token响应失败: {e}")
            return None
    else:
        logging.error(f"登录失败，状态码: {response.status_code if response else 'None'}")
        return None

def refresh_token_if_needed(token):
    """
    检查令牌是否需要刷新（兼容旧代码）
    """
    if not token:
        return get_access_token()
    return token

def handle_token_error():
    """
    处理令牌错误，强制刷新令牌
    """
    global _cached_token, _token_expire_time
    logging.warning("检测到令牌失效，正在刷新...")
    _cached_token = None
    _token_expire_time = None
    return get_access_token(force_refresh=True)

def clear_user_cache():
    """
    清理过期的用户缓存
    """
    global _user_cache
    current_time = time.time()
    expired_keys = []

    for key, cached_data in _user_cache.items():
        if current_time - cached_data['timestamp'] > _cache_expire_time:
            expired_keys.append(key)

    for key in expired_keys:
        del _user_cache[key]

    if expired_keys:
        logging.info(f"清理了 {len(expired_keys)} 个过期缓存项")

def get_user_info(user_id, token):
    """
    根据用户ID查询用户信息 - 使用精确查询
    """
    try:
        # 使用分页接口的id参数进行精确查询
        user_url = f"{BASE_URL}/admin-api/system/mbr/user/page?pageNo=1&pageSize=10&deleted=0&id={user_id}&tenantId={TENANT_ID}"
        headers = {
            'Authorization': f'Bearer {token}',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Referer': f'{BASE_URL}/mbr/users',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
            'agent-tenant-id': str(TENANT_ID),
            'login_platform': 'web',
            'tenant-id': str(TENANT_ID)
        }

        logging.info(f"查询用户信息: URL={user_url}")

        response = stable_request('get', user_url, headers=headers)

        if response and response.status_code == 401:
            # 令牌失效，尝试刷新
            logging.warning("用户查询令牌失效，尝试刷新")
            new_token = handle_token_error()
            if new_token:
                headers['Authorization'] = f'Bearer {new_token}'
                response = stable_request('get', user_url, headers=headers)

        if response and response.status_code == 200:
            try:
                response_data = response.json()
                logging.info(f"用户查询响应: {response_data}")

                if response_data.get('code') == 0:
                    users = response_data.get('data', {}).get('list', [])
                    total = response_data.get('data', {}).get('total', 0)

                    logging.info(f"查询结果: 总数={total}, 返回用户数={len(users)}")

                    if users and len(users) > 0:
                        user = users[0]  # 精确查询应该只返回一个用户
                        logging.info(f"查询用户信息成功: 用户ID {user_id}, 手机号: {user.get('mobile', 'N/A')}, 订单数: {user.get('channelNum', 0)}")
                        return user
                    else:
                        logging.warning(f"未找到用户ID为 {user_id} 的用户")
                        return None
                else:
                    error_msg = response_data.get('msg', '未知错误')
                    logging.error(f"查询用户信息失败: {error_msg}")
                    return None

            except (KeyError, TypeError) as e:
                logging.error(f"解析用户查询响应失败: {e}")
                return None
        else:
            error_msg = f"查询用户信息失败，状态码: {response.status_code if response else 'None'}"
            if response:
                try:
                    error_detail = response.json()
                    logging.error(f"{error_msg}, 响应内容: {error_detail}")
                except:
                    logging.error(f"{error_msg}, 响应文本: {response.text}")
            else:
                logging.error(error_msg)
            return None

    except Exception as e:
        logging.error(f"查询用户信息异常: {e}", exc_info=True)
        return None

def determine_user_type(user_info):
    """
    判断用户类型 - 返回简化的用户信息（不含注册时间）
    """
    if not user_info:
        return "未知用户"

    # 从API响应中获取字段，支持多种字段名
    channel_num = user_info.get('channelNum', 0) or user_info.get('orderNum', 0) or 0
    balance = user_info.get('balance', 0) or 0

    # 转换余额为元（如果是分为单位）
    balance_yuan = balance / 100.0 if balance > 1000 else balance

    # 返回简化的用户信息
    return f"{channel_num}单 | 余额{balance_yuan:.2f}元"

def send_message(order_no, user_id, final_recharge_amount, custom_message=None):
    """
    发送消息通知
    """
    url = "https://aldsidle.agiso.com/api/Workbench/SendMsg"
    headers = {
        'User-Agent': ("Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 "
                       "(KHTML, like Gecko) IdleWorkbench/1.4.2 Chrome/106.0.5249.199 "
                       "Electron/21.4.4 Safari/537.36"),
        'Accept': "application/json, text/plain, */*",
        'Authorization': f"Bearer {SEND_MSG_TOKEN}",
        'X-Requested-With': "XMLHttpRequest",
        'Content-Type': "application/json",
        'Origin': "https://aldsidle.agiso.com",
    }

    # 如果有自定义消息，直接发送
    if custom_message:
        payload = json.dumps({"text": custom_message, "tid": order_no})
        response = stable_request('post', url, headers=headers, data=payload)
        if response and response.status_code == 200:
            logging.info("自定义消息发送成功: %s", custom_message)
        else:
            error_msg = f"自定义消息发送失败: {custom_message}"
            if response:
                error_msg += f" (状态码: {response.status_code})"
                if response.status_code == 401:
                    error_msg += " - Token可能已过期，请检查send_msg_token配置"
            logging.error(error_msg)
        return

    # 从配置文件读取消息内容并替换变量
    messages = []
    for i in range(1, 4):  # 读取message_1到message_3
        msg = config.get('Messages', f'message_{i}', fallback='')
        if msg:
            # 替换消息中的变量
            msg = msg.format(
                user_id=user_id,
                final_recharge_amount=final_recharge_amount
            )
            messages.append(msg)

    # 发送每条消息
    for message in messages:
        payload = json.dumps({"text": message, "tid": order_no})
        response = stable_request('post', url, headers=headers, data=payload)
        if response and response.status_code == 200:
            logging.info("消息发送成功: %s", message)
        else:
            error_msg = f"消息发送失败: {message}"
            if response:
                error_msg += f" (状态码: {response.status_code})"
                if response.status_code == 401:
                    error_msg += " - Token可能已过期，请检查send_msg_token配置"
            logging.error(error_msg)

def get_order_by_order_no(order_no, token):
    """
    根据订单号查询订单信息
    """
    try:
        current_timestamp = int(time.time())
        # 查询最近30天的订单
        thirty_days_ago = current_timestamp - 30 * 24 * 3600
        body_data = {
            "order_status": 12,
            "pay_time": [thirty_days_ago, current_timestamp],
            "order_no": order_no
        }
        body_json = json.dumps(body_data, separators=(',', ':'))
        sign = gen_sign(body_json, current_timestamp, APP_ID, APP_SECRET)
        order_url = f"{ORDER_BASE_URL}/api/open/order/list?appid={APP_ID}&timestamp={current_timestamp}&sign={sign}"

        order_response = stable_request(
            'post',
            order_url,
            headers={"Content-Type": "application/json", "Authorization": f'Bearer {token}'},
            data=body_json
        )

        if order_response and order_response.status_code == 200:
            response_data = order_response.json()
            logging.debug(f"订单查询API响应: {response_data}")
            orders = response_data.get('data', {}).get('list', [])
            logging.info(f"查询到 {len(orders)} 个订单")

            for order in orders:
                if order['order_no'] == order_no:
                    logging.info(f"找到匹配订单: {order_no}, 支付金额: {order.get('pay_amount', 'N/A')}")
                    return order

            logging.warning(f"未找到订单号为 {order_no} 的订单")
            return None
        else:
            error_msg = f"查询订单失败，状态码: {order_response.status_code if order_response else 'None'}"
            if order_response:
                try:
                    error_detail = order_response.json()
                    logging.error(f"{error_msg}, 响应内容: {error_detail}")
                except:
                    logging.error(error_msg)
            else:
                logging.error(error_msg)
            return None
    except Exception as e:
        logging.error(f"查询订单异常: {e}")
        return None

def get_user_by_mobile(mobile, token):
    """
    根据手机号查询用户信息 - 带缓存优化版本
    """
    global _user_cache

    # 检查缓存
    cache_key = f"mobile_{mobile}"
    if cache_key in _user_cache:
        cached_data = _user_cache[cache_key]
        if time.time() - cached_data['timestamp'] < _cache_expire_time:
            logging.debug(f"使用缓存的用户信息: {mobile}")
            return cached_data['data']

    try:
        user_url = f"{BASE_URL}/admin-api/system/mbr/user/page?pageNo=1&pageSize=10&deleted=0&mobile={mobile}&tenantId={TENANT_ID}"
        headers = {
            'Authorization': f'Bearer {token}',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Referer': f'{BASE_URL}/mbr/users',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
            'agent-tenant-id': str(TENANT_ID),
            'login_platform': 'web',
            'tenant-id': str(TENANT_ID)
        }

        response = stable_request('get', user_url, headers=headers, timeout=3)  # 减少超时时间

        if response and response.status_code == 200:
            response_data = response.json()
            if response_data.get('code') == 0:
                users = response_data.get('data', {}).get('list', [])
                if users and len(users) > 0:
                    user_info = users[0]
                    # 缓存结果
                    _user_cache[cache_key] = {
                        'data': user_info,
                        'timestamp': time.time()
                    }
                    return user_info

        # 缓存空结果，避免重复查询
        _user_cache[cache_key] = {
            'data': None,
            'timestamp': time.time()
        }
        return None
    except Exception as e:
        logging.error(f"查询用户信息异常: {e}")
        return None

def get_user_info_by_id(user_id, token):
    """
    根据用户ID查询用户信息
    """
    try:
        # 先尝试直接获取用户信息
        user_url = f"{BASE_URL}/admin-api/system/mbr/user/get?id={user_id}&tenantId={TENANT_ID}"
        headers = {
            'Authorization': f'Bearer {token}',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Referer': f'{BASE_URL}/mbr/users',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
            'agent-tenant-id': str(TENANT_ID),
            'login_platform': 'web',
            'tenant-id': str(TENANT_ID)
        }

        response = stable_request('get', user_url, headers=headers)
        logging.info(f"查询用户ID {user_id} 的API响应状态: {response.status_code if response else 'None'}")

        if response and response.status_code == 200:
            response_data = response.json()
            logging.info(f"用户查询响应: {response_data}")
            if response_data.get('code') == 0:
                return response_data.get('data')
            else:
                logging.warning(f"用户查询失败: {response_data.get('msg', '未知错误')}")

        # 如果直接查询失败，尝试通过分页查询
        logging.info(f"直接查询失败，尝试通过分页查询用户ID {user_id}")
        page_url = f"{BASE_URL}/admin-api/system/mbr/user/page?pageNo=1&pageSize=10&deleted=0&id={user_id}&tenantId={TENANT_ID}"

        response = stable_request('get', page_url, headers=headers)
        if response and response.status_code == 200:
            response_data = response.json()
            if response_data.get('code') == 0:
                users = response_data.get('data', {}).get('list', [])
                if users and len(users) > 0:
                    return users[0]

        return None
    except Exception as e:
        logging.error(f"根据ID查询用户信息异常: {e}")
        return None

def get_all_orders(token, days=7):
    """
    获取最近几天的所有订单 - 添加缓存机制减少API调用
    """
    global _orders_cache, _orders_cache_time, _orders_cache_expire

    current_time = time.time()

    # 检查缓存是否有效
    if (_orders_cache and
        current_time - _orders_cache_time < _orders_cache_expire):
        logging.debug("使用缓存的订单数据")
        return _orders_cache

    try:
        current_timestamp = int(time.time())
        # 查询最近指定天数的订单
        days_ago = current_timestamp - days * 24 * 3600
        body_data = {
            "order_status": 12,
            "pay_time": [days_ago, current_timestamp],
            "page": 1,
            "size": 10  # 限制返回10个订单
        }
        body_json = json.dumps(body_data, separators=(',', ':'))
        sign = gen_sign(body_json, current_timestamp, APP_ID, APP_SECRET)
        order_url = f"{ORDER_BASE_URL}/api/open/order/list?appid={APP_ID}&timestamp={current_timestamp}&sign={sign}"

        order_response = stable_request(
            'post',
            order_url,
            headers={"Content-Type": "application/json", "Authorization": f'Bearer {token}'},
            data=body_json
        )

        if order_response and order_response.status_code == 200:
            response_data = order_response.json()
            orders = response_data.get('data', {}).get('list', [])
            logging.info(f"查询到 {len(orders)} 个订单")

            # 过滤满足最低金额要求的订单（移除用户查询逻辑）
            filtered_orders = []

            for order in orders:
                pay_amount_yuan = round(float(order.get('pay_amount', 0)) / 100.0, 2)
                if pay_amount_yuan >= MIN_ORDER_AMOUNT:
                    order['pay_amount_yuan'] = pay_amount_yuan
                    order['estimated_recharge'] = round(pay_amount_yuan + EXTRA_CHARGE, 2)
                    # 确保buyer_eid字段被保存
                    buyer_eid = order.get('buyer_eid')
                    if not buyer_eid:
                        logging.warning(f"订单 {order.get('order_no')} 缺少buyer_eid字段")
                    filtered_orders.append(order)

            logging.info(f"跳过用户信息查询，直接处理订单")

            logging.info(f"满足条件的订单: {len(filtered_orders)} 个")

            # 调试：打印第一个订单的结构
            if filtered_orders:
                first_order = filtered_orders[0]
                logging.info(f"第一个订单数据结构: {list(first_order.keys())}")
                logging.info(f"第一个订单用户信息: user_id={first_order.get('user_id')}, user_type={first_order.get('user_type')}")

            # 缓存订单数据
            _orders_cache = filtered_orders
            _orders_cache_time = current_time

            return filtered_orders
        else:
            error_msg = f"查询订单列表失败，状态码: {order_response.status_code if order_response else 'None'}"
            if order_response:
                try:
                    error_detail = order_response.json()
                    logging.error(f"{error_msg}, 响应内容: {error_detail}")
                except:
                    logging.error(error_msg)
            else:
                logging.error(error_msg)
            return []
    except Exception as e:
        logging.error(f"查询订单列表异常: {e}")
        return []

def check_user_eligibility(user_id, buyer_eid, token):
    """
    检查用户是否符合充值条件
    返回: (is_eligible, reason)
    """
    try:
        logging.info(f"[用户检查] 开始检查用户资格 - 用户ID: {user_id}, buyer_eid: {buyer_eid}")

        # 1. 检查用户余额和充值记录
        logging.info(f"[用户检查] 步骤1: 获取用户信息")
        user_info = get_user_info_by_id(user_id, token)
        if not user_info:
            logging.error(f"[用户检查] 步骤1失败: 无法获取用户ID {user_id} 的信息")
            return False, "无法获取用户信息"

        logging.info(f"[用户检查] 步骤1成功: 获取到用户信息")

        # 获取用户余额（转换为元）
        balance = user_info.get('balance', 0)
        balance_yuan = balance / 100.0 if balance > 1000 else balance
        logging.info(f"[用户检查] 步骤2: 检查用户余额 - 原始余额: {balance}, 转换后余额: {balance_yuan:.2f}元")

        # 检查余额是否为0
        if balance_yuan > 0:
            reason = f"用户余额不为0（当前余额: {balance_yuan:.2f}元），判定为老用户"
            logging.warning(f"[用户检查] 步骤2失败: {reason}")
            return False, reason
        logging.info(f"[用户检查] 步骤2通过: 用户余额为0")

        # 2. 检查黑名单
        logging.info(f"[用户检查] 步骤3: 检查黑名单 - buyer_eid: {buyer_eid}")
        if buyer_eid:
            is_blacklisted = check_buyer_in_blacklist(buyer_eid)
            logging.info(f"[用户检查] 步骤3结果: buyer_eid {buyer_eid} {'在' if is_blacklisted else '不在'}黑名单中")
            if is_blacklisted:
                reason = f"买家已在黑名单中（buyer_eid: {buyer_eid}），判定为老用户"
                logging.warning(f"[用户检查] 步骤3失败: {reason}")
                return False, reason
        else:
            logging.warning(f"[用户检查] 步骤3跳过: buyer_eid为空，无法检查黑名单")
        logging.info(f"[用户检查] 步骤3通过: 不在黑名单中")

        # 3. 检查订单数限制
        channel_num = user_info.get('channelNum', 0) or user_info.get('orderNum', 0) or 0
        logging.info(f"[用户检查] 步骤4: 检查订单数限制 - 当前订单数: {channel_num}, 限制: {NEW_USER_MAX_ORDERS}")
        if channel_num > NEW_USER_MAX_ORDERS:
            reason = f"订单数超过限制（当前: {channel_num}单，限制: {NEW_USER_MAX_ORDERS}单），判定为老用户"
            logging.warning(f"[用户检查] 步骤4失败: {reason}")
            return False, reason
        logging.info(f"[用户检查] 步骤4通过: 订单数符合要求")

        success_reason = f"检查通过，符合新用户条件（余额: {balance_yuan:.2f}元, 订单数: {channel_num}单, 不在黑名单）"
        logging.info(f"[用户检查] 所有检查通过: {success_reason}")
        return True, success_reason

    except Exception as e:
        error_msg = f"检查用户资格时发生异常: {str(e)}"
        logging.error(f"[用户检查] 检查异常: {e}", exc_info=True)
        return False, error_msg

def manual_recharge_user(user_id, recharge_amount, order_no, token):
    """
    手动充值用户余额
    """
    try:
        logging.info(f"[充值API] 开始调用充值API - 用户ID: {user_id}, 金额: {recharge_amount}元, 订单号: {order_no}")

        recharge_url = f"{BASE_URL}/admin-api/kuaidi/mbr/user/recharge"
        recharge_data = {
            'mbrId': int(user_id),  # 确保用户ID是整数
            'balanceType': "1",
            'balance': str(recharge_amount),
            'changeReason': "1",
            'bizType': "balanceRecharge",
            'remark': order_no
        }

        # 记录充值请求信息
        logging.info(f"[充值API] 请求充值: 用户ID={user_id}, 金额={recharge_amount}元")

        logging.info(f"[充值API] 发送充值请求...")
        recharge_response = stable_request(
            'put',
            recharge_url,
            headers={'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'},
            json=recharge_data
        )

        # 记录响应状态
        if recharge_response:
            logging.info(f"[充值API] 响应状态码: {recharge_response.status_code}")
            if recharge_response.status_code != 200:
                try:
                    response_data = recharge_response.json()
                    logging.error(f"[充值API] 错误响应: {response_data}")
                except:
                    logging.error(f"[充值API] 错误响应文本: {recharge_response.text}")
        else:
            logging.error("[充值API] 响应为空")

        # 处理令牌失效的情况
        if recharge_response and recharge_response.status_code == 401:
            logging.warning("充值请求令牌失效，尝试刷新令牌后重试")
            new_token = handle_token_error()
            if new_token:
                # 使用新令牌重试
                recharge_response = stable_request(
                    'put',
                    recharge_url,
                    headers={'Authorization': f'Bearer {new_token}', 'Content-Type': 'application/json'},
                    json=recharge_data
                )
                logging.info(f"重试充值响应状态码: {recharge_response.status_code if recharge_response else 'None'}")

        if recharge_response and recharge_response.status_code == 200:
            # 验证响应内容
            try:
                response_data = recharge_response.json()
                if response_data.get('code') == 0 or response_data.get('success') == True:
                    logging.info(f"手动充值成功: 用户ID {user_id}, 充值金额 {recharge_amount}元, 订单号 {order_no}")
                    return True, "充值成功"
                else:
                    error_msg = f"充值失败: {response_data.get('msg', '未知错误')}"
                    logging.error(error_msg)
                    return False, error_msg
            except:
                # 如果无法解析JSON，但状态码是200，认为成功
                logging.info(f"手动充值成功: 用户ID {user_id}, 充值金额 {recharge_amount}元, 订单号 {order_no}")
                return True, "充值成功"
        else:
            error_msg = f"充值失败，状态码: {recharge_response.status_code if recharge_response else 'None'}"
            if recharge_response:
                try:
                    error_detail = recharge_response.json().get('msg', '未知错误')
                    error_msg += f", 错误详情: {error_detail}"
                except:
                    error_msg += f", 响应内容: {recharge_response.text}"
            logging.error(error_msg)
            return False, error_msg
    except Exception as e:
        error_msg = f"充值异常: {e}"
        logging.error(error_msg, exc_info=True)
        return False, error_msg

# ========== 日志队列处理，用于GUI实时显示日志 ==========
class ManualRechargeQueueHandler(logging.Handler):
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        try:
            # 只处理手动充值相关的重要日志
            if (record.levelno >= logging.INFO and
                self.is_manual_recharge_log(record) and
                not self.is_debug_noise(record)):
                msg = self.format(record)
                self.log_queue.put(msg)
        except Exception:
            self.handleError(record)

    def is_debug_noise(self, record):
        """判断是否为不需要显示的调试信息 - 扩展过滤规则"""
        noise_keywords = [
            'Starting new HTTPS connection', 'https://', 'HTTP/1.1',
            'DEBUG', '生成的签名字符串', 'POST /api', 'GET /admin-api',
            '使用缓存', '查询手机号', '第一个订单数据结构', '订单列表查询API响应',
            '用户查询响应', '清理了', '个过期缓存项', '开始批量查询',
            '请求URL:', '请求数据:', '请求头:', '响应内容:', '响应文本:',
            'buyer_eid:', '获取到用户信息:', '订单详细信息:'
        ]
        message = record.getMessage()
        return any(keyword in message for keyword in noise_keywords)

    def is_manual_recharge_log(self, record):
        """判断是否为手动充值相关的日志"""
        manual_keywords = [
            '手动充值', '订单查询API响应', '找到匹配订单', '未找到订单号',
            '查询到', '个订单', '开始手动充值', '使用自定义充值金额',
            '自动计算充值金额', '手动充值消息通知', '手动充值日志已清空',
            '查询用户信息', '用户查询', '登录成功', '获取到新Token'
        ]
        message = record.getMessage()
        return any(keyword in message for keyword in manual_keywords)

# ========== GUI部分 ==========
class ManualRechargeApp:
    def __init__(self, master):
        self.master = master
        self.master.title("手动充值工具")
        self.master.geometry("1000x700")

        # 初始化变量
        self.current_orders = []  # 存储当前订单数据
        self.last_refresh_time = 0  # 上次刷新时间
        self.min_refresh_interval = MIN_REFRESH_INTERVAL  # 使用配置文件中的最小刷新间隔
        self.recharge_records = []  # 存储充值记录

        # 配置变量
        self.extra_charge_var = tk.StringVar(value=str(EXTRA_CHARGE))

        # 用户输入状态控制
        self.user_is_editing = False  # 标记用户是否正在编辑
        self.editing_timeout_id = None  # 编辑超时定时器ID

        # 防重策略：记录正在充值的订单
        self.recharging_orders = set()  # 正在充值的订单号集合
        self.recharging_buttons = {}  # 记录正在充值的按钮，用于状态更新

        # 编辑框滚动跟随相关变量
        self._editing_item = None  # 当前编辑的行
        self._editing_column = None  # 当前编辑的列
        self._scroll_callback = None  # 滚动回调函数

        self.style = ttk.Style()
        self.style.theme_use('default')

        # ========== 订单列表和充值功能区 ==========
        main_frame = ttk.Frame(master)
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 上方控制区
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill='x', pady=(0, 10))

        # 左侧按钮区
        left_control = ttk.Frame(control_frame)
        left_control.pack(side='left', fill='x', expand=True)

        # 刷新按钮和状态
        ttk.Button(left_control, text="立即刷新", command=self.refresh_orders).pack(side='left', padx=5)

        # 自动刷新控制
        auto_frame = ttk.Frame(left_control)
        auto_frame.pack(side='left', padx=10)

        self.auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(auto_frame, text="自动刷新", variable=self.auto_refresh_var,
                       command=self.toggle_auto_refresh).pack(side='left')

        ttk.Label(auto_frame, text="间隔:").pack(side='left', padx=(5, 2))
        self.refresh_interval_var = tk.IntVar(value=AUTO_REFRESH_INTERVAL)  # 使用配置文件中的默认间隔
        ttk.Spinbox(auto_frame, from_=MIN_REFRESH_INTERVAL, to=60, width=5,  # 使用配置文件中的最小间隔
                   textvariable=self.refresh_interval_var,
                   command=self.update_refresh_interval).pack(side='left')
        ttk.Label(auto_frame, text="秒").pack(side='left', padx=(2, 0))

        self.refresh_status_var = tk.StringVar(value=f"自动刷新已启动 (间隔{AUTO_REFRESH_INTERVAL}秒)")
        self.status_label = ttk.Label(left_control, textvariable=self.refresh_status_var, foreground='blue')
        self.status_label.pack(side='left', padx=10)

        # 添加静默刷新指示器
        self.silent_refresh_indicator = ttk.Label(left_control, text="●", foreground='green', font=('Arial', 8))
        self.silent_refresh_indicator.pack(side='left', padx=2)

        # 订单号搜索区
        search_frame = ttk.Frame(left_control)
        search_frame.pack(side='left', padx=10)
        ttk.Label(search_frame, text="订单号:").pack(side='left', padx=2)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=15)
        self.search_entry.pack(side='left', padx=2)
        ttk.Button(search_frame, text="搜索", command=self.search_orders).pack(side='left', padx=2)
        ttk.Button(search_frame, text="清空", command=self.clear_search).pack(side='left', padx=2)

        # 配置按钮 - 放在搜索区域右边
        ttk.Button(search_frame, text="⚙️ 配置", command=self.show_config_dialog).pack(side='left', padx=10)

        # 绑定搜索框回车事件
        self.search_entry.bind('<Return>', lambda e: self.search_orders())

        # 订单列表区域（占满整个空间）
        orders_frame = ttk.Labelframe(main_frame, text="订单列表")
        orders_frame.pack(fill='both', expand=True)

        # 创建主容器，包含表格和按钮
        table_container = ttk.Frame(orders_frame)
        table_container.pack(fill='both', expand=True)

        # 创建Treeview表格（新结构：订单号-买家昵称-实付金额-ID-充值金额-操作）
        columns = ('order_no', 'buyer_nick', 'pay_amount', 'user_id', 'recharge_amount', 'action')
        self.orders_tree = ttk.Treeview(table_container, columns=columns, show='headings', height=15)

        # 设置行高（增加三分之一）
        style = ttk.Style()
        style.configure("Treeview", rowheight=int(20 * 1.33))  # 默认20px增加到约27px

        # 配置新订单的颜色标记
        self.orders_tree.tag_configure('new_order', background='#E8F5E8', foreground='#2E8B57')  # 浅绿色背景
        self.orders_tree.tag_configure('normal_order', background='white', foreground='black')  # 正常颜色

        # 配置选中行的颜色为蓝色
        style.configure("Treeview", selectbackground='#0078D4', selectforeground='white')  # 蓝色选中背景

        # 设置列标题
        self.orders_tree.heading('buyer_nick', text='买家昵称')
        self.orders_tree.heading('order_no', text='订单号')
        self.orders_tree.heading('pay_amount', text='实付金额(元)')
        self.orders_tree.heading('user_id', text='ID')
        self.orders_tree.heading('recharge_amount', text='充值金额(元)')
        self.orders_tree.heading('action', text='操作')

        # 设置列宽和对齐方式
        self.orders_tree.column('buyer_nick', width=100, anchor='w')  # 左对齐
        self.orders_tree.column('order_no', width=140, anchor='w')    # 左对齐
        self.orders_tree.column('pay_amount', width=90, anchor='center')  # 居中对齐
        self.orders_tree.column('user_id', width=80, anchor='center')     # 居中对齐
        self.orders_tree.column('recharge_amount', width=100, anchor='center')  # 居中对齐
        self.orders_tree.column('action', width=150, anchor='center')     # 居中对齐

        # 添加滚动条
        orders_scrollbar = ttk.Scrollbar(table_container, orient='vertical', command=self.orders_tree.yview)
        self.orders_tree.configure(yscrollcommand=orders_scrollbar.set)

        # 布局表格和滚动条
        self.orders_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        orders_scrollbar.pack(side='right', fill='y', pady=5)

        # 绑定单击事件用于选择
        self.orders_tree.bind('<Button-1>', self.on_order_click)
        # 绑定鼠标移动事件用于显示按钮提示
        self.orders_tree.bind('<Motion>', self.on_mouse_motion)

        # 记录当前活动的单元格编辑器，用于点击充值时强制提交
        self._active_cell_editor = None
        self._active_cell_save = None

        # 存储订单数据和选中的订单
        self.orders_data = []
        self.selected_order = None

        # ========== 充值记录列表 ==========
        records_frame = ttk.Labelframe(master, text="充值记录")
        records_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 创建充值记录表格
        records_container = ttk.Frame(records_frame)
        records_container.pack(fill='both', expand=True)

        # 充值记录表格列：ID(隐藏)、时间、订单号、买家昵称、用户ID、充值金额、充值类型、状态、失败原因、操作
        record_columns = ('db_id', 'time', 'order_no', 'buyer_nick', 'user_id', 'amount', 'type', 'status', 'reason', 'action')
        self.records_tree = ttk.Treeview(records_container, columns=record_columns, show='headings', height=8)

        # 充值记录表格也使用相同的行高设置
        self.records_tree.configure(style="Treeview")

        # 设置充值记录列标题
        self.records_tree.heading('db_id', text='')  # 隐藏的数据库ID列
        self.records_tree.heading('time', text='充值时间')
        self.records_tree.heading('order_no', text='订单号')
        self.records_tree.heading('buyer_nick', text='买家昵称')
        self.records_tree.heading('user_id', text='用户ID')
        self.records_tree.heading('amount', text='充值金额(元)')
        self.records_tree.heading('type', text='充值类型')
        self.records_tree.heading('status', text='状态')
        self.records_tree.heading('reason', text='失败原因')
        self.records_tree.heading('action', text='操作')

        # 设置充值记录列宽和对齐方式
        self.records_tree.column('db_id', width=0, minwidth=0)           # 隐藏的数据库ID列
        self.records_tree.column('time', width=120, anchor='center')      # 居中对齐
        self.records_tree.column('order_no', width=120, anchor='w')       # 左对齐
        self.records_tree.column('buyer_nick', width=100, anchor='w')     # 左对齐
        self.records_tree.column('user_id', width=80, anchor='center')    # 居中对齐
        self.records_tree.column('amount', width=90, anchor='center')     # 居中对齐
        self.records_tree.column('type', width=80, anchor='center')       # 居中对齐
        self.records_tree.column('status', width=60, anchor='center')     # 居中对齐
        self.records_tree.column('reason', width=150, anchor='w')         # 左对齐，失败原因列
        self.records_tree.column('action', width=60, anchor='center')     # 居中对齐

        # 添加滚动条
        records_scrollbar = ttk.Scrollbar(records_container, orient='vertical', command=self.records_tree.yview)
        self.records_tree.configure(yscrollcommand=records_scrollbar.set)

        # 布局充值记录表格和滚动条
        self.records_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        records_scrollbar.pack(side='right', fill='y', pady=5)

        # 绑定充值记录点击事件
        self.records_tree.bind('<Button-1>', self.on_record_click)



        # 初始化自动刷新
        self.auto_refresh_job = None
        self.new_orders_timestamp = {}  # 记录新订单的发现时间

        # 启动时延迟刷新订单列表，减少启动时的CPU负载
        self.master.after(3000, self.refresh_orders)  # 延迟到3秒后

        # 启动自动刷新
        self.start_auto_refresh()

        # 从数据库加载历史充值记录（在界面创建完成后）
        self.master.after(1000, self.load_recharge_records_from_db)  # 延迟1秒后加载







    # ============================ 订单列表相关逻辑 ============================
    def refresh_orders(self):
        """
        刷新订单列表 - 添加频率限制以减少CPU负载
        """
        current_time = time.time()
        # 检查是否满足最小刷新间隔
        if current_time - self.last_refresh_time < self.min_refresh_interval:
            remaining = self.min_refresh_interval - (current_time - self.last_refresh_time)
            self.refresh_status_var.set(f"刷新过于频繁，请等待 {remaining:.1f} 秒")
            return

        self.last_refresh_time = current_time
        self.refresh_status_var.set("正在获取最新订单...")
        # 在后台线程中获取订单
        threading.Thread(target=self._do_refresh_orders, daemon=True).start()

    def toggle_auto_refresh(self):
        """
        切换自动刷新状态
        """
        if self.auto_refresh_var.get():
            self.start_auto_refresh()
            interval = self.refresh_interval_var.get()
            self.refresh_status_var.set(f"自动刷新已启动 (间隔{interval}秒)")
        else:
            self.stop_auto_refresh()
            self.refresh_status_var.set("自动刷新已停止")

    def start_auto_refresh(self):
        """
        启动自动刷新
        """
        if self.auto_refresh_var.get():
            interval = self.refresh_interval_var.get() * 1000  # 转换为毫秒
            self.auto_refresh_job = self.master.after(interval, self._auto_refresh_callback)

    def stop_auto_refresh(self):
        """
        停止自动刷新
        """
        if self.auto_refresh_job:
            self.master.after_cancel(self.auto_refresh_job)
            self.auto_refresh_job = None

    def _auto_refresh_callback(self):
        """
        自动刷新回调
        """
        if self.auto_refresh_var.get():
            self.refresh_orders()
            # 安排下次刷新
            self.start_auto_refresh()

    def update_refresh_interval(self):
        """
        更新刷新间隔
        """
        if self.auto_refresh_var.get():
            # 重新启动自动刷新以应用新间隔
            self.stop_auto_refresh()
            self.start_auto_refresh()

    def _do_refresh_orders(self):
        """
        在后台线程中获取订单列表 - 静默刷新
        """
        try:
            # 检查用户是否正在编辑，如果是则跳过本次刷新
            if self.user_is_editing:
                logging.info("用户正在编辑数据，跳过本次自动刷新")
                return

            # 显示刷新指示器
            self.master.after(0, lambda: self._show_refresh_indicator())

            # 清理过期缓存
            clear_user_cache()

            # 获取访问令牌
            token = get_access_token()
            if not token:
                self.master.after(0, lambda: self.refresh_status_var.set("获取Token失败"))
                self.master.after(0, lambda: self._hide_refresh_indicator())
                return

            # 获取订单列表 - 获取最近1天的最新10个订单
            orders = get_all_orders(token, days=1)  # 获取最近1天的订单

            # 在主线程中更新界面
            self.master.after(0, lambda: self._update_orders_display(orders))
            self.master.after(0, lambda: self._hide_refresh_indicator())

        except Exception as e:
            error_msg = f"获取订单失败: {str(e)}"
            logging.error(f"获取订单列表异常: {e}")
            self.master.after(0, lambda: self.refresh_status_var.set(error_msg))

    def _update_orders_display(self, orders):
        """
        更新订单列表显示 - 静默刷新优化
        """
        if orders is None:
            self.refresh_status_var.set("获取订单失败")
            return

        # 检查新订单并记录时间戳
        new_orders = []
        current_time = time.time()

        if hasattr(self, 'current_orders') and self.current_orders:
            current_order_nos = {order.get('order_no') for order in self.current_orders}
            new_orders = [order for order in orders if order.get('order_no') not in current_order_nos]

            # 记录新订单的发现时间
            for order in new_orders:
                order_no = order.get('order_no')
                if order_no:
                    self.new_orders_timestamp[order_no] = current_time

        # 清理超过1分钟的新订单记录
        expired_orders = []
        for order_no, timestamp in self.new_orders_timestamp.items():
            if current_time - timestamp > 60:  # 60秒 = 1分钟
                expired_orders.append(order_no)

        for order_no in expired_orders:
            del self.new_orders_timestamp[order_no]

        # 保存用户编辑的数据（ID和充值金额）- 无论是否刷新都要保存
        user_edits = {}  # {order_no: {'user_id': value, 'recharge_amount': value}}
        for item in self.orders_tree.get_children():
            values = self.orders_tree.item(item)['values']
            if len(values) >= 5:
                order_no = values[1]  # 订单号
                user_id = values[3]   # 用户ID
                recharge_amount = values[4]  # 充值金额
                # 只保存用户填写的数据（非空）
                if user_id or recharge_amount:
                    user_edits[order_no] = {
                        'user_id': user_id,
                        'recharge_amount': recharge_amount
                    }

        # 静默更新：只有数据真正变化时才更新界面
        if self._orders_changed(orders):
            # 保存当前选中项和滚动位置
            selected_items = self.orders_tree.selection()
            selected_order_no = None
            if selected_items:
                selected_values = self.orders_tree.item(selected_items[0])['values']
                if selected_values:
                    selected_order_no = selected_values[1]  # 使用订单号作为标识

            # 获取当前滚动位置
            try:
                scroll_top = self.orders_tree.yview()[0]
            except:
                scroll_top = 0

            # 清空现有数据
            for item in self.orders_tree.get_children():
                self.orders_tree.delete(item)

            self.orders_data = orders
            self.current_orders = orders  # 同时保存到current_orders变量

        # 添加新数据，并标记新订单（基于1分钟时间窗口）
        for order in orders:
            order_no = order.get('order_no', '')
            pay_amount = order.get('pay_amount_yuan', 0)
            buyer_nick = order.get('buyer_nick', '')
            estimated_recharge = order.get('estimated_recharge', 0)

            # 判断是否为新订单（1分钟内发现的订单）
            tag = 'new_order' if order_no in self.new_orders_timestamp else 'normal_order'

            # 恢复用户编辑的数据（如果存在）
            user_id_value = ''
            recharge_amount_value = ''  # 充值金额列始终保持为空
            if order_no in user_edits:
                user_id_value = user_edits[order_no]['user_id']
                # 不恢复充值金额，保持为空
                # recharge_amount_value = user_edits[order_no]['recharge_amount']

            # 插入列：订单号、买家昵称、实付、ID、充值金额、操作
            # ID恢复用户之前填写的数据，充值金额保持为空
            item = self.orders_tree.insert('', 'end', values=(
                order_no,
                buyer_nick,
                f"{pay_amount:.2f}",
                user_id_value,  # 恢复用户填写的ID
                '',  # 充值金额列保持为空
                "🟢充值 | 🔴强制充值"  # 显示两个按钮选项，用颜色图标区分
            ), tags=(tag,))

        # 更新状态并提醒新订单
        status_text = f"已获取 {len(orders)} 个订单"
        if new_orders:
            status_text += f" (🔔发现 {len(new_orders)} 个新订单!)"
            # 播放系统提示音
            try:
                self.master.bell()
            except:
                pass

        self.refresh_status_var.set(status_text)

        # 如果界面更新了，恢复选中项和滚动位置
        if self._orders_changed(orders):
            # 恢复选中项和滚动位置
            if 'selected_order_no' in locals() and selected_order_no:
                for item in self.orders_tree.get_children():
                    values = self.orders_tree.item(item)['values']
                    if values and len(values) > 1 and values[1] == selected_order_no:  # 使用订单号（第2列）
                        self.orders_tree.selection_set(item)
                        self.orders_tree.focus(item)
                        break

            # 恢复滚动位置
            if 'scroll_top' in locals():
                try:
                    self.orders_tree.yview_moveto(scroll_top)
                except:
                    pass

        # 更新窗口标题显示订单数量和时间（静默更新）
        import datetime
        current_time = datetime.datetime.now().strftime("%H:%M:%S")
        self.master.title(f"手动充值工具 - {len(orders)}个订单 - {current_time}")

    def _orders_changed(self, new_orders):
        """
        检查订单数据是否真正发生变化
        """
        if not hasattr(self, 'current_orders') or not self.current_orders:
            return True

        # 比较订单数量
        if len(new_orders) != len(self.current_orders):
            return True

        # 比较订单号列表
        current_order_nos = {order.get('order_no') for order in self.current_orders}
        new_order_nos = {order.get('order_no') for order in new_orders}

        if current_order_nos != new_order_nos:
            return True

        # 比较订单详细信息（用户ID、用户类型等）
        for new_order in new_orders:
            order_no = new_order.get('order_no')
            current_order = next((o for o in self.current_orders if o.get('order_no') == order_no), None)
            if current_order:
                # 比较关键字段
                key_fields = ['user_id', 'user_type', 'pay_amount_yuan']
                for field in key_fields:
                    if new_order.get(field) != current_order.get(field):
                        return True

        return False

    def _show_refresh_indicator(self):
        """
        显示静默刷新指示器
        """
        try:
            self.silent_refresh_indicator.config(foreground='orange', text='●')
        except:
            pass

    def _hide_refresh_indicator(self):
        """
        隐藏静默刷新指示器
        """
        try:
            self.silent_refresh_indicator.config(foreground='green', text='●')
        except:
            pass

    # ============================ 搜索功能 ============================
    def search_orders(self):
        """
        按订单号搜索订单
        """
        search_text = self.search_var.get().strip()
        if not search_text:
            # 如果搜索框为空，显示所有订单
            self.display_orders(self.current_orders)
            return

        # 过滤订单 - 只搜索订单号
        filtered_orders = []
        for order in self.current_orders:
            order_no = str(order.get('order_no', ''))

            # 检查订单号是否包含搜索文本（不区分大小写）
            if search_text.lower() in order_no.lower():
                filtered_orders.append(order)

        # 显示过滤后的订单
        self.display_orders(filtered_orders)

        if filtered_orders:
            self.refresh_status_var.set(f"找到 {len(filtered_orders)} 个匹配的订单")
        else:
            self.refresh_status_var.set(f"未找到包含 '{search_text}' 的订单号")

    def clear_search(self):
        """
        清空搜索
        """
        self.search_var.set("")
        self.display_orders(self.current_orders)
        self.refresh_status_var.set(f"已获取 {len(self.current_orders)} 个订单")

    def display_orders(self, orders):
        """
        显示订单列表
        """
        # 清空现有数据
        for item in self.orders_tree.get_children():
            self.orders_tree.delete(item)

        # 添加订单数据
        for order in orders:
            user_id = order.get('user_id')

            # 按照列定义顺序插入数据：order_no, buyer_nick, pay_amount, user_id, recharge_amount, action
            # 搜索结果中充值金额默认为空，需要用户手动填写
            self.orders_tree.insert('', 'end', values=(
                order.get('order_no', ''),                      # 订单号
                order.get('buyer_nick', ''),                    # 买家昵称
                f"{order.get('pay_amount_yuan', 0):.2f}",       # 实付金额
                str(user_id) if user_id else '',                # 用户ID
                '',                                             # 充值金额（搜索结果默认为空）
                "🟢充值 | 🔴强制充值"                            # 操作
            ))

    def on_order_double_click(self, event):
        return

    # 充值确认对话框已删除，改为直接在订单列表操作

        # 订单信息
        info_frame = ttk.LabelFrame(main_frame, text="订单信息", padding="5")
        info_frame.pack(fill='x', pady=(0, 5))

        info_grid = ttk.Frame(info_frame)
        info_grid.pack(fill='x')

        ttk.Label(info_grid, text="订单号:", font=('Microsoft YaHei', 8)).grid(row=0, column=0, sticky='w', padx=(0, 5))
        ttk.Label(info_grid, text=order_no, font=('Consolas', 8)).grid(row=0, column=1, sticky='w')

        ttk.Label(info_grid, text="预计充值:", font=('Microsoft YaHei', 8)).grid(row=1, column=0, sticky='w', padx=(0, 5), pady=(2, 0))
        ttk.Label(info_grid, text=f"{estimated_recharge}元", foreground='green', font=('Microsoft YaHei', 8)).grid(row=1, column=1, sticky='w', pady=(2, 0))

        # 用户信息 - 实时查询
        user_frame = ttk.LabelFrame(main_frame, text="用户信息", padding="5")
        user_frame.pack(fill='x', pady=(0, 5))

        # 如果有手机号，先尝试查询用户信息
        if receiver_mobile and receiver_mobile != '待查询':
            # 在后台线程中查询用户信息
            def query_user_info():
                try:
                    logging.info(f"开始查询手机号 {receiver_mobile} 的用户信息")
                    self.master.after(0, lambda: balance_var.set(f"正在查询手机号 {receiver_mobile}..."))

                    token = get_access_token()
                    if token:
                        logging.info(f"获取到Token，开始查询用户信息")
                        user_info = get_user_by_mobile(receiver_mobile, token)
                        if user_info:
                            real_user_id = user_info.get('id')
                            user_type = determine_user_type(user_info)
                            logging.info(f"查询到用户ID: {real_user_id}, 用户类型: {user_type}")
                            self.master.after(0, lambda: user_id_var.set(str(real_user_id)))
                            self.master.after(0, lambda: user_type_var.set(user_type))
                            # 移除用户余额查询
                        else:
                            logging.warning(f"未找到手机号 {receiver_mobile} 对应的用户")
                            self.master.after(0, lambda: user_id_var.set(''))
                            self.master.after(0, lambda: user_type_var.set("未找到用户"))
                            self.master.after(0, lambda: balance_var.set(f"未找到手机号 {receiver_mobile} 的用户"))
                    else:
                        logging.error("获取Token失败")
                        self.master.after(0, lambda: balance_var.set("获取Token失败"))
                except Exception as e:
                    logging.error(f"查询用户信息异常: {e}")
                    self.master.after(0, lambda: balance_var.set(f"查询失败: {str(e)}"))

            threading.Thread(target=query_user_info, daemon=True).start()

        # 用户ID输入
        ttk.Label(user_frame, text="用户ID:", font=('Microsoft YaHei', 8)).pack(anchor='w')
        user_id_var = tk.StringVar(value='')  # 初始为空，等待查询结果
        user_id_entry = ttk.Entry(user_frame, textvariable=user_id_var, font=('Consolas', 8))
        user_id_entry.pack(fill='x', pady=(1, 3))

        # 用户类型显示
        user_type_var = tk.StringVar(value="查询中..." if receiver_mobile else "待查询")
        user_type_frame = ttk.Frame(user_frame)
        user_type_frame.pack(fill='x', pady=(2, 3))
        ttk.Label(user_type_frame, text="用户信息:", font=('Microsoft YaHei', 8)).pack(side='left')
        user_type_label = ttk.Label(user_type_frame, textvariable=user_type_var,
                                   font=('Consolas', 8, 'bold'), foreground='#2E8B57')
        user_type_label.pack(side='left', padx=(5, 0))

        # 动态更新用户类型颜色
        def update_user_type_color():
            current_type = user_type_var.get()
            if "余额0元" in current_type and ("0单" in current_type or "1单" in current_type):
                # 新用户：0-1单且余额为0
                color = '#2E8B57'  # 绿色
            elif "余额0元" not in current_type or ("单" in current_type and not ("0单" in current_type or "1单" in current_type)):
                # 老用户：有余额或订单数>1
                color = '#FF6B35'  # 橙色
            elif current_type in ["未找到用户", "查询失败", "格式错误"]:
                color = '#DC143C'  # 红色
            else:
                color = '#808080'  # 灰色
            user_type_label.config(foreground=color)

        # 绑定用户类型变化事件
        user_type_var.trace_add('write', lambda *args: update_user_type_color())

        # 用户余额显示
        balance_var = tk.StringVar(value="正在查询用户信息..." if receiver_mobile else "请输入用户ID")
        balance_label = ttk.Label(user_frame, textvariable=balance_var, foreground='blue', font=('Microsoft YaHei', 8))
        balance_label.pack(anchor='w')

        # 充值金额
        amount_frame = ttk.LabelFrame(main_frame, text="充值金额", padding="5")
        amount_frame.pack(fill='x', pady=(0, 5))

        ttk.Label(amount_frame, text="自定义金额 (留空使用预计金额):", font=('Microsoft YaHei', 8)).pack(anchor='w')
        custom_amount_var = tk.StringVar()
        custom_amount_entry = ttk.Entry(amount_frame, textvariable=custom_amount_var, font=('Consolas', 8))
        custom_amount_entry.pack(fill='x', pady=(1, 2))

        ttk.Label(amount_frame, text="提示: 支持小数，如 8.50",
                 font=('Microsoft YaHei', 7), foreground='gray').pack(anchor='w')

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(5, 0), side='bottom')



        # 按钮容器 - 确保显示在底部
        btn_container = ttk.Frame(button_frame)
        btn_container.pack(side='bottom', fill='x')

        def on_confirm():
            final_user_id = user_id_var.get().strip()
            if not final_user_id:
                messagebox.showerror("❌ 错误", "请输入用户ID")
                return

            # 验证用户ID格式
            if not final_user_id.isdigit():
                messagebox.showerror("❌ 错误", "用户ID必须是数字")
                return

            # 获取充值金额
            custom_amount = custom_amount_var.get().strip()
            if custom_amount:
                try:
                    final_amount = float(custom_amount)
                    if final_amount <= 0:
                        messagebox.showerror("❌ 错误", "充值金额必须大于0")
                        return
                    if final_amount > 10000:
                        if not messagebox.askyesno("⚠️ 确认", f"充值金额 {final_amount}元 较大，确认继续？"):
                            return
                except ValueError:
                    messagebox.showerror("❌ 错误", "充值金额格式不正确，请输入数字")
                    return
            else:
                final_amount = estimated_recharge

            # 最终确认
            confirm_msg = f"确认充值信息：\n\n📋 订单号: {order_no}\n👤 用户ID: {final_user_id}\n💰 充值金额: {final_amount}元\n\n确认执行充值？"
            if messagebox.askyesno("💰 确认充值", confirm_msg):
                dialog.destroy()
                self.execute_recharge_for_order(final_user_id, final_amount, order_no)

        def on_cancel():
            dialog.destroy()

        # 按钮
        confirm_btn = ttk.Button(btn_container, text="确认充值", command=on_confirm)
        confirm_btn.pack(side='right', padx=(8, 0))

        cancel_btn = ttk.Button(btn_container, text="取消", command=on_cancel)
        cancel_btn.pack(side='right')

        # 绑定用户ID变化事件，自动查询余额和用户类型
        def on_user_id_change(*args):
            current_user_id = user_id_var.get().strip()
            if current_user_id and current_user_id.isdigit():
                balance_var.set("🔍 正在查询余额...")
                user_type_var.set("查询中...")

                # 在后台线程中查询余额和用户类型
                def query_user_info_by_id():
                    try:
                        # 移除用户余额查询

                        # 查询用户类型
                        token = get_access_token()
                        if token:
                            user_info = get_user_info(current_user_id, token)
                            if user_info:
                                user_type = determine_user_type(user_info)
                                self.master.after(0, lambda: user_type_var.set(user_type))
                            else:
                                self.master.after(0, lambda: user_type_var.set("查询失败"))
                    except Exception as e:
                        self.master.after(0, lambda: user_type_var.set("查询异常"))

                threading.Thread(target=query_user_info_by_id, daemon=True).start()
            elif current_user_id and not current_user_id.isdigit():
                balance_var.set("❌ 用户ID格式错误")
                user_type_var.set("格式错误")
            else:
                balance_var.set("💡 请输入用户ID")
                user_type_var.set("待查询")

        user_id_var.trace_add('write', on_user_id_change)

        # 如果通过手机号查询到了用户ID，会自动触发余额查询

        # 键盘快捷键
        dialog.bind('<Return>', lambda e: on_confirm())
        dialog.bind('<Escape>', lambda e: on_cancel())

        # 聚焦到用户ID输入框
        user_id_entry.focus_set()
        user_id_entry.select_range(0, tk.END)

    # query_user_balance 函数已移除，不再查询用户余额

    def execute_recharge_for_order(self, user_id, amount, order_no):
        """
        为指定订单执行充值
        """
        try:
            logging.info(f"开始充值: 用户ID {user_id}, 金额 {amount}元, 订单号 {order_no}")

            # 在新线程中执行充值
            def recharge_thread():
                try:
                    # 获取访问令牌
                    token = get_access_token()
                    if not token:
                        error_msg = "获取访问令牌失败"
                        logging.error(error_msg)
                        self.master.after(0, lambda: messagebox.showerror("充值失败", error_msg))
                        return

                    success, message = manual_recharge_user(user_id, amount, order_no, token)
                    if success:
                        logging.info(f"充值成功: {message}")
                        # 更新订单状态为已充值
                        self.master.after(0, lambda: self.update_order_status(order_no, "已充值"))
                    else:
                        logging.error(f"充值失败: {message}")
                        self.master.after(0, lambda: messagebox.showerror("充值失败", message))
                except Exception as e:
                    error_msg = f"充值异常: {str(e)}"
                    logging.error(error_msg, exc_info=True)
                    self.master.after(0, lambda: messagebox.showerror("充值异常", error_msg))

            thread = threading.Thread(target=recharge_thread, daemon=True)
            thread.start()

        except Exception as e:
            error_msg = f"启动充值失败: {str(e)}"
            logging.error(error_msg, exc_info=True)
            messagebox.showerror("错误", error_msg)

    def update_order_status(self, order_no, status):
        """
        更新订单状态显示
        """
        for item in self.orders_tree.get_children():
            values = self.orders_tree.item(item)['values']
            if values and values[0] == order_no:
                # 更新状态列显示状态
                new_values = list(values)
                new_values[7] = status  # 状态列是第8列（索引7）
                self.orders_tree.item(item, values=new_values)
                break
        selection = self.orders_tree.selection()
        if selection:
            item = selection[0]
            values = self.orders_tree.item(item, 'values')
            if values:
                order_no = values[0]
                pay_amount = values[1]
                estimated_recharge = values[4]

                # 查找对应的订单数据
                for order in self.orders_data:
                    if order.get('order_no') == order_no:
                        self.selected_order = order
                        self.selected_order_var.set(f"订单: {order_no}\n实付: {pay_amount}元\n预计充值: {estimated_recharge}元")

                        # 自动查询用户ID（通过手机号）
                        self._auto_query_user_by_mobile(order.get('receiver_mobile', ''))
                        break

    def _auto_query_user_by_mobile(self, mobile):
        """
        通过手机号自动查询用户ID
        """
        if not mobile:
            return

        # 在后台线程中查询
        threading.Thread(target=self._do_query_user_by_mobile, args=(mobile,), daemon=True).start()

    def _do_query_user_by_mobile(self, mobile):
        """
        在后台线程中通过手机号查询用户
        """
        try:
            # 获取访问令牌
            token = get_access_token()
            if not token:
                return

            # 查询用户信息（通过手机号）
            user_url = f"{BASE_URL}/admin-api/system/mbr/user/page?pageNo=1&pageSize=10&deleted=0&mobile={mobile}&tenantId={TENANT_ID}"
            headers = {
                'Authorization': f'Bearer {token}',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Connection': 'keep-alive',
                'Referer': f'{BASE_URL}/mbr/users',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
                'agent-tenant-id': str(TENANT_ID),
                'login_platform': 'web',
                'tenant-id': str(TENANT_ID)
            }

            response = stable_request('get', user_url, headers=headers)

            if response and response.status_code == 200:
                response_data = response.json()
                if response_data.get('code') == 0:
                    users = response_data.get('data', {}).get('list', [])
                    if users and len(users) > 0:
                        user = users[0]
                        user_id = str(user.get('id', ''))

                        # 在主线程中更新用户ID
                        self.master.after(0, lambda: self.manual_user_id_var.set(user_id))
                        logging.info(f"通过手机号 {mobile} 找到用户ID: {user_id}")

        except Exception as e:
            logging.error(f"通过手机号查询用户异常: {e}")

    # ============================ 手动充值相关逻辑 ============================
    def validate_manual_recharge_input(self):
        """
        验证手动充值输入参数
        """
        if not self.selected_order:
            return False, "请先选择要充值的订单"

        user_id = self.manual_user_id_var.get().strip()
        custom_amount = self.manual_custom_amount_var.get().strip()

        # 验证用户ID
        if not user_id:
            return False, "请输入用户ID"

        if not user_id.isdigit():
            return False, "用户ID必须是数字"

        # 验证自定义金额（如果有输入）
        if custom_amount:
            try:
                amount = float(custom_amount)
                if amount <= 0:
                    return False, "自定义金额必须大于0"
            except ValueError:
                return False, "自定义金额格式不正确"

        return True, "验证通过"

    def execute_manual_recharge(self):
        """
        执行手动充值操作
        """
        # 验证输入
        is_valid, error_msg = self.validate_manual_recharge_input()
        if not is_valid:
            messagebox.showerror("输入错误", error_msg)
            return

        # 获取输入值
        user_id = self.manual_user_id_var.get().strip()
        custom_amount = self.manual_custom_amount_var.get().strip()

        # 检查用户类型（在主线程中进行）
        if not self._check_user_type_for_recharge(user_id, custom_amount):
            return

        # 禁用按钮，防止重复点击
        self.manual_recharge_btn['state'] = 'disabled'
        self.manual_status_var.set("处理中...")
        self.manual_status_label.config(foreground='orange')

        # 在新线程中执行充值操作
        threading.Thread(target=self._do_manual_recharge, daemon=True).start()

    def _check_user_type_for_recharge(self, user_id, custom_amount):
        """
        检查用户类型，对老用户进行特殊处理
        """
        try:
            # 获取访问令牌
            token = get_access_token()
            if not token:
                messagebox.showerror("错误", "获取访问令牌失败，请检查网络连接和配置")
                return False

            # 查询用户信息
            user_info = get_user_info(user_id, token)
            if not user_info:
                messagebox.showerror("错误", f"未找到用户ID为 {user_id} 的用户")
                return False

            # 判断用户类型
            user_type = determine_user_type(user_info)

            # 如果是老用户且没有自定义金额，给出提示
            if "老用户" in user_type and not custom_amount:
                channel_num = user_info.get('channelNum', 0) or user_info.get('orderNum', 0) or 0
                balance = user_info.get('balance', 0) or 0

                # 构建提示信息
                user_info_text = f"用户类型: {user_type}\n订单数: {channel_num}\n余额: {balance}元"

                # 询问是否继续
                result = messagebox.askyesno(
                    "老用户确认",
                    f"{user_info_text}\n\n这是一个老用户，通常需要补差价。\n您确定要继续充值吗？",
                    icon='warning'
                )

                if not result:
                    return False

                # 如果用户确认继续，发送老用户消息
                try:
                    send_message("", user_id, 0, OLD_USER_MESSAGE)
                    logging.info(f"已向老用户 {user_id} 发送提示消息")
                except Exception as e:
                    logging.warning(f"发送老用户消息失败: {e}")

            return True

        except Exception as e:
            logging.error(f"检查用户类型异常: {e}")
            messagebox.showerror("错误", f"检查用户类型时发生异常: {str(e)}")
            return False

    def _do_manual_recharge(self):
        """
        在后台线程中执行手动充值的具体逻辑
        """
        try:
            if not self.selected_order:
                self._update_manual_status("未选择订单", 'red', True)
                return

            order_info = self.selected_order
            order_no = order_info.get('order_no', '')
            user_id = self.manual_user_id_var.get().strip()
            custom_amount = self.manual_custom_amount_var.get().strip()

            # 获取访问令牌
            token = get_access_token()
            if not token:
                self._update_manual_status("获取访问令牌失败", 'red', True)
                return

            # 计算充值金额
            if custom_amount:
                # 使用自定义金额
                final_recharge_amount = float(custom_amount)
                logging.info(f"使用自定义充值金额: {final_recharge_amount}元")
            else:
                # 使用预计算的充值金额
                final_recharge_amount = order_info.get('estimated_recharge', 0)
                logging.info(f"使用预计算充值金额: {final_recharge_amount}元")

            # 执行充值
            logging.info(f"开始手动充值: 订单号 {order_no}, 用户ID {user_id}, 充值金额 {final_recharge_amount}元")
            success, result_msg = manual_recharge_user(user_id, final_recharge_amount, order_no, token)

            if success:
                # 充值成功，发送消息通知
                try:
                    send_message(order_no, user_id, final_recharge_amount)
                    logging.info("手动充值消息通知发送成功")
                except Exception as e:
                    logging.warning(f"手动充值消息通知发送失败: {e}")

                # 保存充值记录
                try:
                    save_to_file({
                        "order_no": order_no,
                        "pay_amount": order_info.get('pay_amount_yuan', 0),
                        "mobile": order_info.get('receiver_mobile', '未知'),
                        "buyer_nick": order_info.get('buyer_nick', '未知'),
                        "final_recharge_amount": final_recharge_amount
                    }, "手动充值成功")
                except Exception as e:
                    logging.warning(f"保存充值记录失败: {e}")

                # 保存buyer_eid到黑名单数据库 (已移动到新的充值流程中)

                self._update_manual_status(f"充值成功！金额: {final_recharge_amount}元", 'green', True)
                # 清空选择和输入
                self.master.after(0, self._clear_manual_inputs)
                # 刷新订单列表
                self.master.after(1000, self.refresh_orders)
            else:
                self._update_manual_status(f"充值失败: {result_msg}", 'red', True)

        except Exception as e:
            error_msg = f"操作异常: {str(e)}"
            logging.error(f"手动充值异常: {e}", exc_info=True)
            self._update_manual_status(error_msg, 'red', True)

    def _update_manual_status(self, message, color, enable_button):
        """
        更新手动充值状态显示
        """
        def update():
            self.manual_status_var.set(message)
            self.manual_status_label.config(foreground=color)
            if enable_button:
                self.manual_recharge_btn['state'] = 'normal'

        self.master.after(0, update)

    def _clear_manual_inputs(self):
        """
        清空手动充值输入框
        """
        self.manual_user_id_var.set("")
        self.manual_custom_amount_var.set("")
        self.user_type_var.set("")
        self.selected_order_var.set("请选择订单")
        self.selected_order = None

        # 清空表格选择
        for item in self.orders_tree.selection():
            self.orders_tree.selection_remove(item)



    def on_user_id_change(self, *_):
        """
        用户ID输入框内容变化时的回调函数
        """
        # 取消之前的延迟查询
        if self.user_query_after_id:
            self.master.after_cancel(self.user_query_after_id)

        # 清空之前的用户类型显示
        self.user_type_var.set("")

        user_id = self.manual_user_id_var.get().strip()
        if user_id and user_id.isdigit() and len(user_id) >= 3:  # 用户ID是数字且长度足够时才查询
            # 延迟1秒后查询，避免频繁查询
            self.user_query_after_id = self.master.after(1000, lambda: self.query_user_type(user_id))

    def query_user_type(self, user_id):
        """
        查询用户类型并显示
        """
        if not user_id or user_id != self.manual_user_id_var.get().strip():
            return  # 用户ID已经改变，不需要查询

        # 在后台线程中查询
        threading.Thread(target=self._do_query_user_type, args=(user_id,), daemon=True).start()

    def _do_query_user_type(self, user_id):
        """
        在后台线程中执行用户类型查询
        """
        try:
            # 显示查询中状态
            self.master.after(0, lambda: self.user_type_var.set("查询中..."))

            # 获取访问令牌
            token = get_access_token()
            if not token:
                self.master.after(0, lambda: self.user_type_var.set("获取Token失败"))
                return

            # 查询用户信息
            user_info = get_user_info(user_id, token)

            if user_info:
                user_type = determine_user_type(user_info)
                # 支持多种字段名
                channel_num = user_info.get('channelNum', 0) or user_info.get('orderNum', 0) or 0
                balance = user_info.get('balance', 0) or 0
                mobile = user_info.get('mobile', 'N/A')

                # 显示详细信息
                display_text = f"{user_type} (平台统计:{channel_num}, 余额:{balance})"
                self.master.after(0, lambda: self.user_type_var.set(display_text))
                logging.info(f"用户 {user_id} 类型: {user_type}, 平台统计: {channel_num}, 余额: {balance}, 手机: {mobile}")
            else:
                self.master.after(0, lambda: self.user_type_var.set("用户不存在"))

        except Exception as e:
            error_msg = "查询失败"
            logging.error(f"查询用户类型异常: {e}")
            self.master.after(0, lambda: self.user_type_var.set(error_msg))




# ========== 程序入口 ==========
    def on_order_click(self, event):
        """
        点击订单行的处理 - 检查是否点击了操作列或用户ID列
        """
        # 获取点击位置
        region = self.orders_tree.identify_region(event.x, event.y)
        if region != "cell":
            return

        # 获取点击的列
        column = self.orders_tree.identify_column(event.x)

        # 列点击处理
        item = self.orders_tree.identify_row(event.y)
        if not item:
            return

        # 在处理点击前，若有活动编辑器，先强制提交
        try:
            if self._active_cell_editor is not None and self._active_cell_save is not None:
                self._active_cell_save()
        except Exception:
            pass
        finally:
            self._active_cell_editor = None
            self._active_cell_save = None

        if column == '#6':  # 操作列
            self.handle_action_click(item, event)
        elif column == '#4':  # ID列
            self.edit_user_id(item, event)
        elif column == '#5':  # 充值金额列
            self.edit_recharge_amount(item, event)

    def handle_action_click(self, item, event):
        """
        处理操作列的点击事件，根据点击位置判断是充值还是强制充值
        """
        # 获取操作列的边界框
        bbox = self.orders_tree.bbox(item, '#6')
        if not bbox:
            return

        # 计算相对于操作列的点击位置
        relative_x = event.x - bbox[0]
        column_width = bbox[2]

        # 操作列显示 "🟢充值 | 🔴强制充值"
        # 根据文本长度估算，"🟢充值 |"约占50%，"🔴强制充值"约占50%
        if relative_x < column_width * 0.5:
            # 点击左侧 - 普通充值
            print(f"点击了普通充值按钮 (位置: {relative_x}/{column_width})")
            self.execute_recharge(item, force=False)
        else:
            # 点击右侧 - 强制充值
            print(f"点击了强制充值按钮 (位置: {relative_x}/{column_width})")
            self.execute_recharge(item, force=True)

    def on_mouse_motion(self, event):
        """
        鼠标移动事件处理，用于显示操作按钮的提示
        """
        # 获取鼠标位置对应的区域
        region = self.orders_tree.identify_region(event.x, event.y)
        if region != "cell":
            return

        # 获取鼠标位置对应的列
        column = self.orders_tree.identify_column(event.x)

        if column == '#6':  # 操作列
            # 获取鼠标位置对应的行
            item = self.orders_tree.identify_row(event.y)
            if not item:
                return

            # 获取操作列的边界框
            bbox = self.orders_tree.bbox(item, '#6')
            if not bbox:
                return

            # 计算相对于操作列的点击位置
            relative_x = event.x - bbox[0]
            column_width = bbox[2]

            # 根据鼠标位置显示不同的提示
            if relative_x < column_width * 0.5:
                # 鼠标在左侧 - 普通充值区域
                self.orders_tree.config(cursor="hand2")
            else:
                # 鼠标在右侧 - 强制充值区域
                self.orders_tree.config(cursor="hand2")
        else:
            # 鼠标不在操作列，恢复默认光标
            self.orders_tree.config(cursor="")

    def cleanup_edit_bindings(self):
        """
        清理编辑时的滚动事件绑定
        """
        try:
            # 解绑滚动事件
            self.orders_tree.unbind('<MouseWheel>')
            self.orders_tree.unbind('<Button-4>')
            self.orders_tree.unbind('<Button-5>')

            # 重新绑定原有的事件
            self.orders_tree.bind('<Button-1>', self.on_order_click)
            self.orders_tree.bind('<Motion>', self.on_mouse_motion)

            # 清理编辑状态
            self._editing_item = None
            self._editing_column = None
            self._scroll_callback = None
        except:
            pass

    def schedule_resume_auto_refresh(self, delay=5000):
        """
        计划恢复自动刷新（延迟指定毫秒后）
        """
        # 取消之前的定时器
        if self.editing_timeout_id:
            self.master.after_cancel(self.editing_timeout_id)

        # 设置新的定时器
        def resume_refresh():
            self.user_is_editing = False
            logging.info("编辑超时，恢复自动刷新")

        self.editing_timeout_id = self.master.after(delay, resume_refresh)

    def edit_user_id(self, item, event):
        """
        编辑用户ID - 新列结构：买家昵称、订单号、实付、用户ID、充值金额、操作
        """
        # 获取当前值 - 用户ID是第4列（索引3）
        values = list(self.orders_tree.item(item)['values'])
        current_user_id = values[3] if len(values) > 3 else ''

        # 获取单元格位置 - 用户ID列是#4
        bbox = self.orders_tree.bbox(item, '#4')
        if not bbox:
            return

        # 设置编辑状态，暂停自动刷新
        self.user_is_editing = True
        if self.editing_timeout_id:
            self.master.after_cancel(self.editing_timeout_id)

        # 创建编辑框
        edit_var = tk.StringVar(value=str(current_user_id))
        edit_entry = ttk.Entry(self.orders_tree, textvariable=edit_var, font=('Consolas', 9))
        edit_entry.place(x=bbox[0], y=bbox[1], width=bbox[2], height=bbox[3])
        edit_entry.focus_set()
        edit_entry.select_range(0, tk.END)

        # 记录活动编辑器，以便点击充值时强制提交
        self._active_cell_editor = edit_entry
        self._active_cell_save = lambda: save_edit()

        # 记录编辑信息，用于滚动时更新位置
        self._editing_item = item
        self._editing_column = '#4'  # ID列

        # 绑定滚动事件，使编辑框跟随滚动
        def update_edit_position(*args):
            if edit_entry.winfo_exists():
                try:
                    new_bbox = self.orders_tree.bbox(item, '#4')
                    if new_bbox:
                        edit_entry.place(x=new_bbox[0], y=new_bbox[1], width=new_bbox[2], height=new_bbox[3])
                    else:
                        # 如果行不可见，隐藏编辑框
                        edit_entry.place_forget()
                except:
                    pass

        # 绑定滚动事件
        self._scroll_callback = update_edit_position
        self.orders_tree.bind('<MouseWheel>', lambda e: self.master.after_idle(update_edit_position))
        self.orders_tree.bind('<Button-4>', lambda e: self.master.after_idle(update_edit_position))
        self.orders_tree.bind('<Button-5>', lambda e: self.master.after_idle(update_edit_position))

        def save_edit():
            try:
                new_user_id = edit_var.get().strip()

                # 验证用户ID格式
                if new_user_id and not new_user_id.isdigit():
                    messagebox.showerror("❌ 错误", "用户ID必须是数字")
                    edit_entry.focus_set()
                    return

                # 立即更新用户ID到第4列（索引3）
                values[3] = new_user_id
                self.orders_tree.item(item, values=values)

                # 强制刷新显示
                self.orders_tree.update_idletasks()

            except Exception as e:
                logging.error(f"保存用户ID失败: {e}")
            finally:
                # 清理滚动事件绑定
                self.cleanup_edit_bindings()
                edit_entry.destroy()
                # 编辑完成，延迟5秒后恢复自动刷新
                self.schedule_resume_auto_refresh()

        def cancel_edit():
            # 清理滚动事件绑定
            self.cleanup_edit_bindings()
            edit_entry.destroy()
            # 取消编辑，延迟5秒后恢复自动刷新
            self.schedule_resume_auto_refresh()

        # 绑定事件 - 立即保存，无延迟
        edit_entry.bind('<Return>', lambda e: save_edit())
        edit_entry.bind('<Escape>', lambda e: cancel_edit())
        edit_entry.bind('<FocusOut>', lambda e: save_edit())

    # auto_query_user_info 函数已移除，不再查询用户信息

    def edit_recharge_amount(self, item, event):
        """
        编辑充值金额 - 新列结构：买家昵称、订单号、实付、用户ID、充值金额、操作
        """
        values = list(self.orders_tree.item(item)['values'])
        current_amount = values[4] if len(values) > 4 else ''

        # 设置编辑状态，暂停自动刷新
        self.user_is_editing = True
        if self.editing_timeout_id:
            self.master.after_cancel(self.editing_timeout_id)

        # 单元格位置 - 充值金额列是#5
        bbox = self.orders_tree.bbox(item, '#5')
        if not bbox:
            return

        edit_var = tk.StringVar(value=str(current_amount))
        edit_entry = ttk.Entry(self.orders_tree, textvariable=edit_var, font=('Consolas', 9))
        edit_entry.place(x=bbox[0], y=bbox[1], width=bbox[2], height=bbox[3])
        edit_entry.focus_set()
        edit_entry.select_range(0, tk.END)

        # 记录活动编辑器
        self._active_cell_editor = edit_entry
        self._active_cell_save = lambda: save_edit()

        # 记录编辑信息，用于滚动时更新位置
        self._editing_item = item
        self._editing_column = '#5'  # 充值金额列

        # 绑定滚动事件，使编辑框跟随滚动
        def update_edit_position(*args):
            if edit_entry.winfo_exists():
                try:
                    new_bbox = self.orders_tree.bbox(item, '#5')
                    if new_bbox:
                        edit_entry.place(x=new_bbox[0], y=new_bbox[1], width=new_bbox[2], height=new_bbox[3])
                    else:
                        # 如果行不可见，隐藏编辑框
                        edit_entry.place_forget()
                except:
                    pass

        # 绑定滚动事件
        self._scroll_callback = update_edit_position
        self.orders_tree.bind('<MouseWheel>', lambda e: self.master.after_idle(update_edit_position))
        self.orders_tree.bind('<Button-4>', lambda e: self.master.after_idle(update_edit_position))
        self.orders_tree.bind('<Button-5>', lambda e: self.master.after_idle(update_edit_position))

        def save_edit():
            try:
                new_amount = edit_var.get().strip()
                # 验证金额格式（允许空值）
                if new_amount:
                    float(new_amount)  # 验证金额格式
                    logging.info(f"用户手动设置充值金额: {new_amount}元")
                else:
                    logging.info("用户清空充值金额，将使用自动计算金额")

                # 立即更新到第5列（索引4）
                values[4] = new_amount
                self.orders_tree.item(item, values=values)
                self.orders_tree.update_idletasks()
            except ValueError:
                messagebox.showerror("❌ 错误", "充值金额格式不正确，请输入数字")
            except Exception as e:
                logging.error(f"保存充值金额失败: {e}")
            finally:
                # 清理滚动事件绑定
                self.cleanup_edit_bindings()
                edit_entry.destroy()
                # 编辑完成，延迟5秒后恢复自动刷新
                self.schedule_resume_auto_refresh()

        def cancel_edit():
            # 清理滚动事件绑定
            self.cleanup_edit_bindings()
            edit_entry.destroy()
            # 取消编辑，延迟5秒后恢复自动刷新
            self.schedule_resume_auto_refresh()

        edit_entry.bind('<Return>', lambda e: save_edit())
        edit_entry.bind('<Escape>', lambda e: cancel_edit())
        edit_entry.bind('<FocusOut>', lambda e: save_edit())

    def execute_recharge(self, item, force=False):
        """
        执行充值操作
        force: True为强制充值，False为普通充值
        """
        values = self.orders_tree.item(item)['values']
        if not values:
            return

        # 表格列：订单号、买家昵称、实付、ID、充值金额、操作
        order_no = values[0]
        buyer_nick = values[1]
        pay_amount = values[2]
        user_id_from_row = str(values[3]).strip() if len(values) > 3 else ''
        recharge_amount_from_row = str(values[4]).strip() if len(values) > 4 else ''

        # 验证必填字段
        if not user_id_from_row:
            messagebox.showerror("错误", "请先填写用户ID")
            return

        try:
            user_id = int(user_id_from_row)
            pay_amount_float = float(pay_amount)
        except ValueError:
            messagebox.showerror("错误", "用户ID必须是数字，实付金额格式错误")
            return

        # 充值金额逻辑：
        # 1. 如果用户手动设置了充值金额，使用设置的金额
        # 2. 如果没有设置，使用自动计算的金额（实付金额 + 加价金额）
        if recharge_amount_from_row:
            # 用户手动设置了充值金额
            try:
                recharge_amount = float(recharge_amount_from_row)
                logging.info(f"[充值流程] 使用手动设置的充值金额: {recharge_amount}元")
            except ValueError:
                messagebox.showerror("错误", "充值金额必须是有效数字")
                return
        else:
            # 自动计算充值金额
            recharge_amount = calculate_recharge_amount(pay_amount_float)
            logging.info(f"[充值流程] 自动计算充值金额: 实付{pay_amount_float}元 + 加价{EXTRA_CHARGE}元 = {recharge_amount}元")

        if force:
            # 强制充值：不校验订单会员，直接充值
            self.execute_force_recharge(user_id, recharge_amount, order_no, buyer_nick, pay_amount)
        else:
            # 普通充值：需要校验
            self.execute_normal_recharge(user_id, recharge_amount, order_no, buyer_nick, pay_amount)

    def execute_normal_recharge(self, user_id, recharge_amount, order_no, buyer_nick, pay_amount):
        """
        执行普通充值（需要检查用户资格）
        """
        def check_and_recharge():
            try:
                logging.info(f"[充值流程] 开始普通充值检查 - 订单号: {order_no}, 用户ID: {user_id}, 充值金额: {recharge_amount}元")

                # 获取Token
                logging.info(f"[充值流程] 步骤1: 获取访问令牌")
                token = get_access_token()
                if not token:
                    logging.error(f"[充值流程] 步骤1失败: 无法获取访问令牌")
                    messagebox.showerror("错误", "无法获取访问令牌，请检查网络连接")
                    return
                logging.info(f"[充值流程] 步骤1成功: 获取到访问令牌")

                # 刷新订单列表确保数据最新
                logging.info(f"[充值流程] 步骤1.5: 刷新订单列表确保数据最新")
                try:
                    global _orders_cache
                    _orders_cache = {}  # 清除缓存
                    fresh_orders = get_all_orders(token, days=7)
                    if fresh_orders:
                        self.current_orders = fresh_orders
                        logging.info(f"[充值流程] 步骤1.5成功: 已刷新订单列表，共 {len(fresh_orders)} 个订单")
                    else:
                        logging.warning(f"[充值流程] 步骤1.5警告: 刷新订单列表失败，使用现有数据")
                except Exception as e:
                    logging.warning(f"[充值流程] 步骤1.5异常: 刷新订单列表失败: {e}")
                    # 继续使用现有订单数据

                # 获取订单信息中的buyer_eid
                logging.info(f"[充值流程] 步骤2: 查找订单中的buyer_eid")
                logging.info(f"[充值流程] 步骤2: 当前订单列表有 {len(self.current_orders)} 个订单")
                logging.info(f"[充值流程] 步骤2: 查找目标订单号: {order_no} (类型: {type(order_no)})")

                buyer_eid = None
                order_info = None
                for i, order in enumerate(self.current_orders):
                    current_order_no = order.get('order_no')
                    if i < 3:  # 只显示前3个订单号用于调试
                        logging.info(f"[充值流程] 步骤2: 检查订单 {i+1}: {current_order_no} (类型: {type(current_order_no)})")

                    # 修复数据类型不匹配问题：统一转换为字符串进行比较
                    if str(current_order_no) == str(order_no):
                        buyer_eid = order.get('buyer_eid')
                        order_info = order
                        logging.info(f"[充值流程] 步骤2: 找到匹配订单，buyer_eid: {buyer_eid}")
                        break

                if order_info:
                    logging.info(f"[充值流程] 步骤2成功: 找到订单信息, buyer_eid: {buyer_eid}")
                    logging.debug(f"[充值流程] 订单详细信息: {order_info}")
                else:
                    logging.warning(f"[充值流程] 步骤2警告: 未找到订单号 {order_no} 的详细信息")

                # 检查用户资格
                logging.info(f"[充值流程] 步骤3: 检查用户资格")
                is_eligible, reason = check_user_eligibility(user_id, buyer_eid, token)
                logging.info(f"[充值流程] 步骤3结果: {'通过' if is_eligible else '拒绝'}, 原因: {reason}")

                if not is_eligible:
                    # 不符合条件，显示老用户提示
                    old_user_msg = f"充值失败：{reason}\n\n{OLD_USER_MESSAGE}"
                    logging.warning(f"[充值流程] 普通充值被拒绝: 用户ID {user_id}, 原因: {reason}")

                    # 记录普通充值被拒绝的情况
                    # 获取买家昵称，优先使用订单信息中的昵称
                    buyer_nick_for_record = order_info.get('buyer_nick', '') if order_info else buyer_nick
                    self.add_recharge_record(order_no, user_id, recharge_amount, "普通充值", "拒绝", buyer_nick_for_record, reason)

                    messagebox.showwarning("老用户提示", old_user_msg)
                    return

                # 符合条件，直接执行充值（已移除确认弹窗）
                logging.info(f"[充值流程] 步骤4: 用户资格检查通过，开始执行充值操作")
                logging.info(f"[充值流程] 充值信息: 订单号={order_no}, 买家昵称={buyer_nick}, 实付金额={pay_amount}元, 用户ID={user_id}, 充值金额={recharge_amount}元")
                logging.info(f"[充值流程] 检查结果: {reason}")

                # 防重策略：检查是否正在充值
                if order_no in self.recharging_orders:
                    logging.warning(f"[充值流程] 防重策略: 订单 {order_no} 正在充值中，跳过重复操作")
                    messagebox.showwarning("重复操作", f"订单 {order_no} 正在充值中，请勿重复点击！")
                    return

                # 添加到正在充值的订单集合
                self.recharging_orders.add(order_no)
                logging.info(f"[充值流程] 防重策略: 已将订单 {order_no} 加入充值队列")

                self.do_recharge(user_id, recharge_amount, order_no, "普通充值", buyer_nick)

            except Exception as e:
                error_msg = f"检查用户资格时发生异常: {str(e)}"
                logging.error(f"[充值流程] 普通充值检查异常: {e}", exc_info=True)
                messagebox.showerror("错误", error_msg)

        # 在后台线程中执行检查
        threading.Thread(target=check_and_recharge, daemon=True).start()

    def execute_force_recharge(self, user_id, recharge_amount, order_no, buyer_nick, pay_amount):
        """
        执行强制充值（不校验用户）
        """
        logging.info(f"[强制充值流程] 开始强制充值 - 订单号: {order_no}, 用户ID: {user_id}, 充值金额: {recharge_amount}元")

        # 刷新订单列表确保数据最新
        logging.info(f"[强制充值流程] 刷新订单列表确保数据最新")
        try:
            token = get_access_token()
            if token:
                global _orders_cache
                _orders_cache = {}  # 清除缓存
                fresh_orders = get_all_orders(token, days=7)
                if fresh_orders:
                    self.current_orders = fresh_orders
                    logging.info(f"[强制充值流程] 已刷新订单列表，共 {len(fresh_orders)} 个订单")
        except Exception as e:
            logging.warning(f"[强制充值流程] 刷新订单列表失败: {e}")

        # 直接执行强制充值（已移除确认弹窗）
        logging.info(f"[强制充值流程] 开始执行强制充值操作")
        logging.info(f"[强制充值流程] 充值信息: 订单号={order_no}, 买家昵称={buyer_nick}, 实付金额={pay_amount}元, 用户ID={user_id}, 充值金额={recharge_amount}元")
        logging.warning(f"[强制充值流程] 注意: 强制充值不会校验用户是否存在！")

        # 防重策略：检查是否正在充值
        if order_no in self.recharging_orders:
            logging.warning(f"[强制充值流程] 防重策略: 订单 {order_no} 正在充值中，跳过重复操作")
            messagebox.showwarning("重复操作", f"订单 {order_no} 正在充值中，请勿重复点击！")
            return

        # 添加到正在充值的订单集合
        self.recharging_orders.add(order_no)
        logging.info(f"[强制充值流程] 防重策略: 已将订单 {order_no} 加入充值队列")

        self.do_recharge(user_id, recharge_amount, order_no, "强制充值", buyer_nick)

    def do_recharge(self, user_id, recharge_amount, order_no, recharge_type, buyer_nick=""):
        """
        执行实际的充值操作
        """
        def recharge_thread():
            try:
                logging.info(f"[{recharge_type}] 开始执行充值操作 - 订单号: {order_no}, 用户ID: {user_id}, 充值金额: {recharge_amount}元")

                # 如果没有传递买家昵称，尝试从订单列表中获取
                logging.info(f"[{recharge_type}] 步骤1: 获取买家昵称和订单信息")
                logging.info(f"[{recharge_type}] 步骤1: 当前订单列表有 {len(self.current_orders)} 个订单")
                logging.info(f"[{recharge_type}] 步骤1: 查找目标订单号: {order_no} (类型: {type(order_no)})")

                if not buyer_nick:
                    temp_buyer_nick = ""
                    order_info = None
                    for i, order in enumerate(self.current_orders):
                        current_order_no = order.get('order_no')
                        if i < 3:  # 只显示前3个订单号用于调试
                            logging.info(f"[{recharge_type}] 步骤1: 检查订单 {i+1}: {current_order_no} (类型: {type(current_order_no)})")

                        # 修复数据类型不匹配问题：统一转换为字符串进行比较
                        if str(current_order_no) == str(order_no):
                            temp_buyer_nick = order.get('buyer_nick', '')
                            order_info = order
                            logging.info(f"[{recharge_type}] 步骤1: 找到匹配订单，买家昵称: {temp_buyer_nick}")
                            break
                    buyer_nick_to_use = temp_buyer_nick
                    logging.info(f"[{recharge_type}] 步骤1: 从订单列表获取买家昵称: {buyer_nick_to_use}")
                else:
                    buyer_nick_to_use = buyer_nick
                    # 仍然需要获取订单信息用于后续的buyer_eid
                    order_info = None
                    for i, order in enumerate(self.current_orders):
                        current_order_no = order.get('order_no')
                        if i < 3:  # 只显示前3个订单号用于调试
                            logging.info(f"[{recharge_type}] 步骤1: 检查订单 {i+1}: {current_order_no} (类型: {type(current_order_no)})")

                        # 修复数据类型不匹配问题：统一转换为字符串进行比较
                        if str(current_order_no) == str(order_no):
                            order_info = order
                            logging.info(f"[{recharge_type}] 步骤1: 找到匹配订单")
                            break
                    logging.info(f"[{recharge_type}] 步骤1: 使用传入的买家昵称: {buyer_nick_to_use}")

                if order_info:
                    buyer_eid = order_info.get('buyer_eid')
                    logging.info(f"[{recharge_type}] 步骤1: 获取到订单信息, buyer_eid: {buyer_eid}")
                else:
                    # 如果在当前订单列表中找不到，尝试重新获取订单信息
                    logging.warning(f"[{recharge_type}] 步骤1: 未找到订单号 {order_no} 的详细信息，尝试重新获取...")
                    buyer_eid = None
                    try:
                        # 强制重新获取订单列表（不使用缓存）
                        temp_token = get_access_token()
                        if temp_token:
                            logging.info(f"[{recharge_type}] 步骤1: 强制刷新订单数据...")
                            # 清除订单缓存，强制重新获取
                            global _orders_cache
                            _orders_cache = {}

                            all_orders = get_all_orders(temp_token, days=7)
                            if all_orders:
                                for order in all_orders:
                                    # 修复数据类型不匹配问题：统一转换为字符串进行比较
                                    if str(order.get('order_no')) == str(order_no):
                                        order_info = order
                                        buyer_eid = order_info.get('buyer_eid')
                                        logging.info(f"[{recharge_type}] 步骤1: 重新获取成功, buyer_eid: {buyer_eid}")
                                        # 更新当前订单列表
                                        self.current_orders = all_orders
                                        break
                                else:
                                    logging.warning(f"[{recharge_type}] 步骤1: 重新获取后仍未找到订单 {order_no}")
                            else:
                                logging.warning(f"[{recharge_type}] 步骤1: 重新获取订单列表失败")
                        else:
                            logging.warning(f"[{recharge_type}] 步骤1: 无法获取访问令牌，跳过重新获取")
                    except Exception as e:
                        logging.error(f"[{recharge_type}] 步骤1: 重新获取订单信息异常: {e}")
                        order_info = None

                logging.info(f"[{recharge_type}] 步骤2: 获取访问令牌")
                token = get_access_token()
                if not token:
                    logging.error(f"[{recharge_type}] 步骤2失败: 获取访问令牌失败")
                    # 记录失败状态
                    self.add_recharge_record(order_no, user_id, recharge_amount, recharge_type, "失败", buyer_nick_to_use, "获取访问令牌失败")
                    self.master.after(0, lambda: messagebox.showerror("错误", "获取访问令牌失败"))
                    return
                logging.info(f"[{recharge_type}] 步骤2成功: 获取到访问令牌")

                logging.info(f"[{recharge_type}] 步骤3: 执行充值API调用")
                success, message = manual_recharge_user(user_id, recharge_amount, order_no, token)
                logging.info(f"[{recharge_type}] 步骤3结果: {'成功' if success else '失败'}, 消息: {message}")

                if success:
                    logging.info(f"[{recharge_type}] 步骤4: 充值成功，开始后续处理")

                    # 记录成功状态
                    self.add_recharge_record(order_no, user_id, recharge_amount, recharge_type, "成功", buyer_nick_to_use)

                    # 保存buyer_eid到黑名单数据库
                    if order_info and order_info.get('buyer_eid'):
                        logging.info(f"[{recharge_type}] 步骤4a: 保存buyer_eid到黑名单数据库")
                        try:
                            add_buyer_to_blacklist(
                                buyer_eid=order_info.get('buyer_eid'),
                                order_no=order_no,
                                buyer_nick=buyer_nick_to_use,
                                user_id=str(user_id),
                                recharge_amount=recharge_amount,
                                notes=f"{recharge_type}成功，金额: {recharge_amount}元"
                            )
                            logging.info(f"[{recharge_type}] 步骤4a成功: buyer_eid {order_info.get('buyer_eid')} 已保存到黑名单数据库")
                        except Exception as e:
                            logging.error(f"[{recharge_type}] 步骤4a失败: 保存buyer_eid到黑名单失败: {e}")
                    else:
                        logging.warning(f"[{recharge_type}] 步骤4a跳过: 订单缺少buyer_eid，无法保存到黑名单")

                    # 显示成功消息
                    self.master.after(0, lambda: messagebox.showinfo("成功", f"{recharge_type}成功！\n用户ID：{user_id}\n充值金额：{recharge_amount}元"))

                    # 发送消息通知
                    logging.info(f"[{recharge_type}] 步骤4b: 发送消息通知")
                    try:
                        send_message(order_no, user_id, recharge_amount)
                        logging.info(f"[{recharge_type}] 步骤4b成功: 消息通知发送完成")
                    except Exception as e:
                        logging.warning(f"[{recharge_type}] 步骤4b失败: 消息通知发送失败: {e}")

                    # 充值成功后清除订单列表中对应行的ID和充值金额
                    self.master.after(0, lambda: self.clear_order_fields(order_no))
                    logging.info(f"[{recharge_type}] 充值流程完成")
                else:
                    logging.error(f"[{recharge_type}] 步骤3失败: 充值失败: {message}")
                    # 记录失败状态
                    self.add_recharge_record(order_no, user_id, recharge_amount, recharge_type, "失败", buyer_nick_to_use, f"充值API失败: {message}")
                    self.master.after(0, lambda: messagebox.showerror("失败", f"{recharge_type}失败：{message}"))

            except Exception as e:
                error_msg = f"{recharge_type}异常：{str(e)}"
                logging.error(f"[{recharge_type}] 充值流程异常: {e}", exc_info=True)
                # 记录异常状态
                self.add_recharge_record(order_no, user_id, recharge_amount, recharge_type, "异常", buyer_nick_to_use, f"系统异常: {str(e)}")
                self.master.after(0, lambda: messagebox.showerror("异常", error_msg))
            finally:
                # 无论成功还是失败，都要移除防重标记
                if order_no in self.recharging_orders:
                    self.recharging_orders.remove(order_no)
                    logging.info(f"[{recharge_type}] 防重策略: 已将订单 {order_no} 从充值队列中移除")

        # 在后台线程执行充值
        threading.Thread(target=recharge_thread, daemon=True).start()

    def clear_order_fields(self, order_no):
        """
        清除订单列表中指定订单的ID和充值金额字段
        """
        try:
            # 遍历订单列表，找到对应的订单行
            for item in self.orders_tree.get_children():
                values = list(self.orders_tree.item(item)['values'])
                if values and len(values) > 1 and values[0] == order_no:  # 订单号在第一列
                    # 清除ID和充值金额（第4列和第5列）
                    if len(values) > 4:
                        values[3] = ''  # 清除用户ID
                        values[4] = ''  # 清除充值金额
                        self.orders_tree.item(item, values=values)
                    break
        except Exception as e:
            logging.error(f"清除订单字段失败: {e}")

    def add_recharge_record(self, order_no, user_id, amount, recharge_type, status="成功", buyer_nick="", reason=""):
        """
        添加充值记录到数据库

        参数:
        - order_no: 订单号
        - user_id: 用户ID
        - amount: 充值金额
        - recharge_type: 充值类型（普通充值/强制充值）
        - status: 状态（成功/失败/异常/拒绝）
        - buyer_nick: 买家昵称
        - reason: 失败原因（仅在失败时使用）
        """
        from datetime import datetime

        recharge_time = datetime.now()
        failure_reason = reason if status != "成功" else None

        # 保存到数据库
        try:
            record_id = db_add_recharge_record(
                recharge_time=recharge_time,
                order_no=order_no,
                buyer_nick=buyer_nick,
                user_id=str(user_id),
                amount=float(amount),
                recharge_type=recharge_type,
                status=status,
                failure_reason=failure_reason
            )

            if record_id:
                logging.info(f"充值记录已保存到数据库: id={record_id}, order_no={order_no}, status={status}")
                # 临时存储记录ID，用于界面显示时关联
                self._last_record_id = record_id
            else:
                logging.error(f"充值记录保存到数据库失败: order_no={order_no}")
                # 降级处理：保存到文件
                self.save_recharge_record_to_file_fallback(recharge_time, order_no, buyer_nick, user_id, amount, recharge_type, status, failure_reason)
                self._last_record_id = None

        except Exception as e:
            logging.error(f"充值记录数据库操作异常: {e}")
            # 降级处理：保存到文件
            self.save_recharge_record_to_file_fallback(recharge_time, order_no, buyer_nick, user_id, amount, recharge_type, status, failure_reason)

        # 构建记录对象用于界面显示
        record = {
            'time': recharge_time.strftime('%Y-%m-%d %H:%M:%S'),
            'order_no': order_no,
            'buyer_nick': buyer_nick,
            'user_id': user_id,
            'amount': amount,
            'type': recharge_type,
            'status': status,
            'reason': failure_reason or ""
        }

        self.recharge_records.append(record)

        # 根据状态设置颜色标签
        status_tag = 'success' if status == "成功" else 'failed'

        # 配置状态颜色
        self.records_tree.tag_configure('success', foreground='green')
        self.records_tree.tag_configure('failed', foreground='red')

        # 更新充值记录表格
        db_id = getattr(self, '_last_record_id', '') or ''  # 获取数据库ID
        item = self.records_tree.insert('', 0, values=(  # 插入到顶部
            db_id,  # 数据库ID（隐藏列）
            record['time'],
            record['order_no'],
            record['buyer_nick'],
            record['user_id'],
            f"{record['amount']:.2f}",
            record['type'],
            record['status'],
            record['reason'],  # 失败原因
            "扣除" if status == "成功" else "-"
        ), tags=(status_tag,))

        # 清理临时变量
        if hasattr(self, '_last_record_id'):
            delattr(self, '_last_record_id')

        # 保存到文件
        self.save_recharge_record_to_file(record)

    def save_recharge_record_to_file_fallback(self, recharge_time, order_no, buyer_nick, user_id, amount, recharge_type, status, failure_reason):
        """
        降级处理：保存充值记录到文件（当数据库不可用时）
        """
        try:
            time_str = recharge_time.strftime('%Y-%m-%d %H:%M:%S')
            record_line = f"{time_str} | 订单号:{order_no} | 买家昵称:{buyer_nick} | 用户ID:{user_id} | 充值金额:{amount}元 | 类型:{recharge_type} | 状态:{status}"

            # 如果有失败原因，添加到记录中
            if failure_reason and failure_reason.strip():
                record_line += f" | 失败原因:{failure_reason}"

            record_line += " | [数据库降级保存]\n"

            with open("recharge_records_fallback.txt", "a", encoding='utf-8') as file:
                file.write(record_line)

            logging.warning(f"充值记录已降级保存到文件: {order_no}")

        except Exception as e:
            logging.error(f"降级保存充值记录失败: {e}")

    def load_recharge_records_from_db(self):
        """从数据库加载历史充值记录到界面（后台线程）"""
        def load_thread():
            try:
                logging.info("开始从数据库加载充值记录...")
                db_records = get_all_recharge_records(limit=200)  # 首次加载 200 条
                logging.info(f"[记录加载] 数据库返回 {len(db_records)} 条记录（后台线程）")

                # 在主线程中更新界面
                def update_ui():
                    try:
                        # 清空现有记录
                        for item in self.records_tree.get_children():
                            self.records_tree.delete(item)

                        # 配置状态标签颜色
                        self.records_tree.tag_configure('success', foreground='green')
                        self.records_tree.tag_configure('failed', foreground='red')
                        self.records_tree.tag_configure('error', foreground='red')
                        self.records_tree.tag_configure('warning', foreground='orange')
                        self.records_tree.tag_configure('normal', foreground='black')

                        # 添加数据库记录到界面
                        for db_record in db_records:
                            # 确定状态标签
                            status = db_record['status']
                            if status == "成功":
                                status_tag = 'success'
                            elif status == "已扣除":
                                status_tag = 'warning'
                            elif status in ["失败", "异常"]:
                                status_tag = 'failed'
                            elif status == "拒绝":
                                status_tag = 'warning'
                            else:
                                status_tag = 'normal'

                            # 确定操作列显示
                            action_text = "扣除" if status == "成功" else "-"

                            # 插入到表格，数据库ID放在第一列（隐藏）
                            item = self.records_tree.insert('', 0, values=(
                                db_record['id'],  # 数据库ID（隐藏列）
                                db_record['recharge_time'].strftime('%Y-%m-%d %H:%M:%S'),
                                db_record['order_no'],
                                db_record['buyer_nick'],
                                db_record['user_id'],
                                f"{db_record['amount']:.2f}",
                                db_record['recharge_type'],
                                db_record['status'],
                                db_record['failure_reason'] or '',  # 确保不是 None
                                action_text
                            ), tags=(status_tag,))

                        logging.info(f"成功从数据库加载 {len(db_records)} 条充值记录")

                    except Exception as e:
                        logging.error(f"更新界面失败: {e}")

                # 在主线程中执行界面更新
                self.master.after(0, update_ui)

            except Exception as e:
                logging.error(f"从数据库加载充值记录失败: {e}")
                # 降级处理：在主线程中执行
                self.master.after(0, self.load_recharge_records_from_file_fallback)

        # 在后台线程中执行数据库查询
        import threading
        threading.Thread(target=load_thread, daemon=True).start()

    def load_recharge_records_from_file_fallback(self):
        """降级处理：从文件加载充值记录"""
        try:
            logging.warning("尝试从文件加载充值记录（降级处理）...")
            # 这里可以实现从文件加载的逻辑，暂时跳过
            logging.info("文件加载功能暂未实现，将显示空记录列表")
        except Exception as e:
            logging.error(f"从文件加载充值记录失败: {e}")

    def save_recharge_record_to_file(self, record):
        """
        保存充值记录到文件（保留兼容性）
        """
        try:
            # 构建记录行，包含失败原因
            record_line = f"{record['time']} | 订单号:{record['order_no']} | 买家昵称:{record['buyer_nick']} | 用户ID:{record['user_id']} | 充值金额:{record['amount']}元 | 类型:{record['type']} | 状态:{record['status']}"

            # 如果有失败原因，添加到记录中
            if record.get('reason') and record['reason'].strip():
                record_line += f" | 失败原因:{record['reason']}"

            record_line += "\n"

            with open("recharge_records.txt", "a", encoding='utf-8') as file:
                file.write(record_line)
        except Exception as e:
            logging.error(f"保存充值记录失败: {e}")

    def on_record_click(self, event):
        """
        点击充值记录的处理
        """
        # 获取点击位置
        region = self.records_tree.identify_region(event.x, event.y)
        if region != "cell":
            return

        # 获取点击的列
        column = self.records_tree.identify_column(event.x)

        # 获取点击的行
        item = self.records_tree.identify_row(event.y)
        if not item:
            return

        if column == '#10':  # 操作列（扣除）- 修复：添加数据库ID列后，操作列从#9变为#10
            self.execute_deduct(item)

    def execute_deduct(self, item):
        """
        执行扣除操作
        """
        values = self.records_tree.item(item)['values']
        if not values:
            return

        # 列结构：db_id, time, order_no, buyer_nick, user_id, amount, type, status, reason, action
        db_id = values[0]  # 数据库ID（隐藏列）
        time_str = values[1]
        order_no = values[2]
        buyer_nick = values[3]
        user_id = values[4]
        amount = values[5]
        recharge_type = values[6]
        status = values[7]
        # values[8] 是失败原因列，跳过
        # values[9] 是操作列

        # 只有成功的充值记录才能扣除
        if status != "成功":
            messagebox.showwarning("无法扣除", f"只有成功的充值记录才能扣除！\n当前状态：{status}")
            return

        # 确认扣除
        confirm_msg = f"确认扣除充值记录？\n\n时间：{time_str}\n订单号：{order_no}\n买家昵称：{buyer_nick}\n用户ID：{user_id}\n金额：{amount}元\n类型：{recharge_type}\n状态：{status}\n\n扣除后将从用户余额中减去相应金额！"

        if messagebox.askyesno("扣除确认", confirm_msg):
            self.do_deduct(user_id, amount, order_no, item)

    def do_deduct(self, user_id, amount, order_no, tree_item):
        """
        执行实际的扣除操作
        """
        def deduct_thread():
            try:
                # 获取数据库记录ID（从隐藏的第一列）
                values = self.records_tree.item(tree_item)['values']
                db_record_id = values[0] if values else None

                token = get_access_token()
                if not token:
                    self.master.after(0, lambda: messagebox.showerror("错误", "获取访问令牌失败"))
                    return

                # 扣除金额（负数充值）
                deduct_amount = -float(amount.replace('元', ''))
                success, message = manual_recharge_user(user_id, deduct_amount, f"扣除-{order_no}", token)

                if success:
                    # 标记记录为已扣除（不删除记录）
                    if db_record_id and str(db_record_id).isdigit():
                        try:
                            from recharge_records_db import get_recharge_db
                            db = get_recharge_db()
                            updated = db.mark_deducted(int(db_record_id))
                            if updated:
                                logging.info(f"已将记录标记为已扣除: id={db_record_id}")
                            else:
                                logging.warning(f"未能标记记录为已扣除: id={db_record_id}")
                        except Exception as e:
                            logging.error(f"标记记录已扣除失败: {e}")

                    # 更新界面：状态改为“已扣除”，操作列禁用
                    def update_ui_after_deduct():
                        try:
                            row_values = list(self.records_tree.item(tree_item)['values'])
                            # 列结构：db_id, time, order_no, buyer_nick, user_id, amount, type, status, reason, action
                            row_values[7] = "已扣除"  # 状态列
                            row_values[9] = "-"      # 操作列禁用
                            self.records_tree.item(tree_item, values=row_values, tags=("warning",))
                            messagebox.showinfo("成功", f"扣除成功！\n用户ID：{user_id}\n扣除金额：{amount}元")
                        except Exception as e:
                            logging.error(f"更新界面状态失败: {e}")
                    self.master.after(0, update_ui_after_deduct)

                    # 记录扣除操作到文件（保留备份）
                    from datetime import datetime
                    deduct_record = f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 扣除操作 | 订单号:{order_no} | 用户ID:{user_id} | 扣除金额:{amount}元 | 数据库ID:{db_record_id} | 已扣除\n"
                    with open("deduct_records.txt", "a", encoding='utf-8') as file:
                        file.write(deduct_record)
                else:
                    self.master.after(0, lambda: messagebox.showerror("失败", f"扣除失败：{message}"))

            except Exception as e:
                error_msg = f"扣除异常：{str(e)}"
                logging.error(error_msg)
                self.master.after(0, lambda: messagebox.showerror("异常", error_msg))

        # 在后台线程执行扣除
        threading.Thread(target=deduct_thread, daemon=True).start()

    def show_config_dialog(self):
        """
        显示配置参数对话框
        """
        # 创建配置对话框
        config_dialog = tk.Toplevel(self.master)
        config_dialog.title("配置参数")
        config_dialog.geometry("700x700")
        config_dialog.resizable(False, False)

        # 设置对话框图标和属性
        config_dialog.transient(self.master)
        config_dialog.grab_set()

        # 居中显示
        config_dialog.update_idletasks()
        x = (config_dialog.winfo_screenwidth() // 2) - (700 // 2)
        y = (config_dialog.winfo_screenheight() // 2) - (700 // 2)
        config_dialog.geometry(f"700x700+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(config_dialog, padding="10")
        main_frame.pack(fill='both', expand=True)

        # ========== 登录配置 ==========
        login_frame = ttk.LabelFrame(main_frame, text="登录配置", padding="10")
        login_frame.pack(fill='x', pady=(0, 10))

        # 读取当前登录配置
        login_config = configparser.ConfigParser()
        login_config.read('manual_recharge_config.ini', encoding='utf-8')

        current_username = login_config.get('Login', 'username', fallback='')
        current_password = login_config.get('Login', 'password', fallback='')
        current_tenant_id = login_config.get('Login', 'tenant_id', fallback='')

        # 用户名
        ttk.Label(login_frame, text="用户名:", font=('Arial', 9, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        username_var = tk.StringVar(value=current_username)
        username_entry = ttk.Entry(login_frame, textvariable=username_var, width=20)
        username_entry.grid(row=0, column=1, sticky='w', padx=(10, 5), pady=5)

        # 密码
        ttk.Label(login_frame, text="密码:", font=('Arial', 9, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        password_var = tk.StringVar(value=current_password)
        password_entry = ttk.Entry(login_frame, textvariable=password_var, width=20, show='*')
        password_entry.grid(row=1, column=1, sticky='w', padx=(10, 5), pady=5)

        # 显示/隐藏密码按钮
        def toggle_password():
            if password_entry['show'] == '*':
                password_entry['show'] = ''
                show_pwd_btn.config(text='隐藏')
            else:
                password_entry['show'] = '*'
                show_pwd_btn.config(text='显示')

        show_pwd_btn = ttk.Button(login_frame, text="显示", command=toggle_password, width=6)
        show_pwd_btn.grid(row=1, column=2, sticky='w', padx=(5, 0), pady=5)

        # 租户ID
        ttk.Label(login_frame, text="租户ID:", font=('Arial', 9, 'bold')).grid(row=2, column=0, sticky='w', pady=5)
        tenant_id_var = tk.StringVar(value=current_tenant_id)
        tenant_id_entry = ttk.Entry(login_frame, textvariable=tenant_id_var, width=20)
        tenant_id_entry.grid(row=2, column=1, sticky='w', padx=(10, 5), pady=5)

        # ========== 凭证配置 ==========
        credentials_frame = ttk.LabelFrame(main_frame, text="凭证配置", padding="10")
        credentials_frame.pack(fill='x', pady=(0, 10))

        # 读取当前凭证配置
        credentials_config = configparser.ConfigParser()
        credentials_config.read('manual_recharge_config.ini', encoding='utf-8')

        current_app_id = credentials_config.get('Credentials', 'app_id', fallback='')
        current_app_secret = credentials_config.get('Credentials', 'app_secret', fallback='')
        current_send_msg_token = credentials_config.get('Credentials', 'send_msg_token', fallback='')

        # APP_ID
        ttk.Label(credentials_frame, text="APP_ID:", font=('Arial', 9, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        app_id_var = tk.StringVar(value=current_app_id)
        app_id_entry = ttk.Entry(credentials_frame, textvariable=app_id_var, width=30)
        app_id_entry.grid(row=0, column=1, sticky='w', padx=(10, 5), pady=5)

        # APP_SECRET
        ttk.Label(credentials_frame, text="APP_SECRET:", font=('Arial', 9, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        app_secret_var = tk.StringVar(value=current_app_secret)
        app_secret_entry = ttk.Entry(credentials_frame, textvariable=app_secret_var, width=30, show='*')
        app_secret_entry.grid(row=1, column=1, sticky='w', padx=(10, 5), pady=5)

        # 显示/隐藏APP_SECRET按钮
        def toggle_app_secret():
            if app_secret_entry['show'] == '*':
                app_secret_entry['show'] = ''
                show_secret_btn.config(text='隐藏')
            else:
                app_secret_entry['show'] = '*'
                show_secret_btn.config(text='显示')

        show_secret_btn = ttk.Button(credentials_frame, text="显示", command=toggle_app_secret, width=6)
        show_secret_btn.grid(row=1, column=2, sticky='w', padx=(5, 0), pady=5)

        # SEND_MSG_TOKEN
        ttk.Label(credentials_frame, text="SEND_MSG_TOKEN:", font=('Arial', 9, 'bold')).grid(row=2, column=0, sticky='w', pady=5)
        send_msg_token_var = tk.StringVar(value=current_send_msg_token)
        send_msg_token_entry = ttk.Entry(credentials_frame, textvariable=send_msg_token_var, width=30, show='*')
        send_msg_token_entry.grid(row=2, column=1, sticky='w', padx=(10, 5), pady=5)

        # 显示/隐藏SEND_MSG_TOKEN按钮
        def toggle_send_msg_token():
            if send_msg_token_entry['show'] == '*':
                send_msg_token_entry['show'] = ''
                show_token_btn.config(text='隐藏')
            else:
                send_msg_token_entry['show'] = '*'
                show_token_btn.config(text='显示')

        show_token_btn = ttk.Button(credentials_frame, text="显示", command=toggle_send_msg_token, width=6)
        show_token_btn.grid(row=2, column=2, sticky='w', padx=(5, 0), pady=5)

        # ========== 基础配置 ==========
        basic_frame = ttk.LabelFrame(main_frame, text="基础配置", padding="10")
        basic_frame.pack(fill='x', pady=(0, 10))

        # BASE_URL
        ttk.Label(basic_frame, text="BASE_URL:", font=('Arial', 9, 'bold')).grid(row=0, column=0, sticky='w', pady=2)
        ttk.Label(basic_frame, text=BASE_URL, foreground='blue').grid(row=0, column=1, sticky='w', padx=(10, 0), pady=2)

        # APP_ID
        ttk.Label(basic_frame, text="APP_ID:", font=('Arial', 9, 'bold')).grid(row=1, column=0, sticky='w', pady=2)
        ttk.Label(basic_frame, text=APP_ID, foreground='blue').grid(row=1, column=1, sticky='w', padx=(10, 0), pady=2)

        # ========== 充值配置 ==========
        charge_frame = ttk.LabelFrame(main_frame, text="充值配置", padding="10")
        charge_frame.pack(fill='x', pady=(0, 10))

        # 加价金额
        ttk.Label(charge_frame, text="加价金额:", font=('Arial', 9, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        extra_charge_entry = ttk.Entry(charge_frame, textvariable=self.extra_charge_var, width=15)
        extra_charge_entry.grid(row=0, column=1, sticky='w', padx=(10, 5), pady=5)
        ttk.Label(charge_frame, text="元", foreground='gray').grid(row=0, column=2, sticky='w', pady=5)

        # 最低订单金额
        ttk.Label(charge_frame, text="最低订单金额:", font=('Arial', 9, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        ttk.Label(charge_frame, text=f"{MIN_ORDER_AMOUNT}元", foreground='blue').grid(row=1, column=1, sticky='w', padx=(10, 0), pady=5)

        # ========== 性能配置 ==========
        perf_frame = ttk.LabelFrame(main_frame, text="性能优化配置", padding="10")
        perf_frame.pack(fill='x', pady=(0, 10))

        # 创建性能配置的网格
        perf_configs = [
            ("自动刷新间隔:", f"{AUTO_REFRESH_INTERVAL}秒"),
            ("最小刷新间隔:", f"{MIN_REFRESH_INTERVAL}秒"),
            ("最大用户查询数:", f"{MAX_USER_QUERIES}个"),
            ("请求超时时间:", f"{REQUEST_TIMEOUT}秒"),
            ("请求重试次数:", f"{REQUEST_RETRIES}次"),
            ("缓存过期时间:", f"{CACHE_EXPIRE_TIME//60}分钟"),
            ("订单缓存时间:", f"{ORDERS_CACHE_EXPIRE}秒")
        ]

        for i, (label, value) in enumerate(perf_configs):
            row = i // 2
            col = (i % 2) * 3
            ttk.Label(perf_frame, text=label, font=('Arial', 8, 'bold')).grid(row=row, column=col, sticky='w', pady=2, padx=(0, 5))
            ttk.Label(perf_frame, text=value, foreground='green').grid(row=row, column=col+1, sticky='w', pady=2, padx=(0, 20))

        # ========== 操作按钮 ==========
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(10, 0))

        # 保存登录配置函数
        def save_login_config():
            try:
                # 读取配置文件
                login_config = configparser.ConfigParser()
                login_config.read('manual_recharge_config.ini', encoding='utf-8')

                # 更新登录配置
                if not login_config.has_section('Login'):
                    login_config.add_section('Login')

                login_config.set('Login', 'username', username_var.get().strip())
                login_config.set('Login', 'password', password_var.get().strip())
                login_config.set('Login', 'tenant_id', tenant_id_var.get().strip())

                # 保存配置文件
                with open('manual_recharge_config.ini', 'w', encoding='utf-8') as configfile:
                    login_config.write(configfile)

                messagebox.showinfo("成功", "登录配置已保存！\n重启程序后生效。")
                logging.info("登录配置已更新")

            except Exception as e:
                messagebox.showerror("错误", f"保存登录配置失败: {e}")
                logging.error(f"保存登录配置失败: {e}")

        # 保存凭证配置函数
        def save_credentials_config():
            try:
                # 验证输入
                app_id = app_id_var.get().strip()
                app_secret = app_secret_var.get().strip()
                send_msg_token = send_msg_token_var.get().strip()

                if not app_id:
                    messagebox.showerror("错误", "APP_ID不能为空")
                    return
                if not app_secret:
                    messagebox.showerror("错误", "APP_SECRET不能为空")
                    return
                if not send_msg_token:
                    messagebox.showerror("错误", "SEND_MSG_TOKEN不能为空")
                    return

                # 读取配置文件
                credentials_config = configparser.ConfigParser()
                credentials_config.read('manual_recharge_config.ini', encoding='utf-8')

                # 更新凭证配置
                if not credentials_config.has_section('Credentials'):
                    credentials_config.add_section('Credentials')

                credentials_config.set('Credentials', 'app_id', app_id)
                credentials_config.set('Credentials', 'app_secret', app_secret)
                credentials_config.set('Credentials', 'send_msg_token', send_msg_token)

                # 保存配置文件
                with open('manual_recharge_config.ini', 'w', encoding='utf-8') as configfile:
                    credentials_config.write(configfile)

                messagebox.showinfo("成功", "凭证配置已保存！\n重启程序后生效。")
                logging.info("凭证配置已更新")

            except Exception as e:
                messagebox.showerror("错误", f"保存凭证配置失败: {e}")
                logging.error(f"保存凭证配置失败: {e}")

        # 保存登录配置按钮
        save_login_btn = ttk.Button(button_frame, text="保存登录配置", command=save_login_config)
        save_login_btn.pack(side='left', padx=(0, 10))

        # 保存凭证配置按钮
        save_credentials_btn = ttk.Button(button_frame, text="保存凭证配置", command=save_credentials_config)
        save_credentials_btn.pack(side='left', padx=(0, 10))

        # 更新加价金额按钮
        update_btn = ttk.Button(button_frame, text="更新加价金额", command=self.update_extra_charge_from_dialog)
        update_btn.pack(side='left', padx=(0, 10))

        # 打开配置文件按钮
        open_config_btn = ttk.Button(button_frame, text="编辑配置文件", command=self.open_config_file)
        open_config_btn.pack(side='left', padx=(0, 10))

        # 关闭按钮
        close_btn = ttk.Button(button_frame, text="关闭", command=config_dialog.destroy)
        close_btn.pack(side='right')

        # ========== 说明信息 ==========
        info_frame = ttk.LabelFrame(main_frame, text="说明", padding="10")
        info_frame.pack(fill='x', pady=(10, 0))

        info_text = """• 登录配置：用户名、密码、租户ID，保存后重启程序生效
• 凭证配置：APP_ID、APP_SECRET、SEND_MSG_TOKEN，保存后重启程序生效
• 基础配置：需要在 manual_recharge_config.ini 文件中修改
• 性能配置：可以在配置文件的 [Performance] 段中调整
• 加价金额：可以在此对话框中直接修改并立即生效
• 修改配置文件后需要重启程序才能生效"""

        ttk.Label(info_frame, text=info_text, foreground='gray', font=('Arial', 8)).pack(anchor='w')

    def update_extra_charge_from_dialog(self):
        """
        从配置对话框更新加价金额
        """
        try:
            new_charge = float(self.extra_charge_var.get())
            global EXTRA_CHARGE
            EXTRA_CHARGE = new_charge

            # 更新配置文件
            config.set('Settings', 'extra_charge', str(new_charge))
            with open('manual_recharge_config.ini', 'w', encoding='utf-8') as configfile:
                config.write(configfile)

            messagebox.showinfo("成功", f"加价金额已更新为: {new_charge}元")
            logging.info(f"加价金额已更新为: {new_charge}元")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")

    def open_config_file(self):
        """
        打开配置文件进行编辑
        """
        import os
        import subprocess

        config_file = "manual_recharge_config.ini"
        try:
            if os.name == 'nt':  # Windows
                os.startfile(config_file)
            elif os.name == 'posix':  # macOS and Linux
                subprocess.call(['open', config_file])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开配置文件: {e}")

    def show_recharge_confirm_dialog(self, order_no, pay_amount, receiver_mobile, buyer_nick, user_id, user_type, estimated_recharge):
        """
        确认充值对话框（仅展示，居中、小窗口）
        """
        dialog = tk.Toplevel(self.master)
        dialog.title("确认充值")
        dialog.resizable(False, False)
        dialog.transient(self.master)
        dialog.grab_set()

        main = ttk.Frame(dialog, padding="10")
        main.pack(fill='both', expand=True)

        # 订单信息
        info = ttk.LabelFrame(main, text="订单", padding="6")
        info.pack(fill='x', pady=(0, 6))
        ttk.Label(info, text="订单号:").grid(row=0, column=0, sticky='w')
        ttk.Label(info, text=order_no, font=('Consolas', 9)).grid(row=0, column=1, sticky='w')
        ttk.Label(info, text="实付:").grid(row=1, column=0, sticky='w')
        ttk.Label(info, text=f"{pay_amount}元").grid(row=1, column=1, sticky='w')
        ttk.Label(info, text="买家:").grid(row=2, column=0, sticky='w')
        ttk.Label(info, text=buyer_nick).grid(row=2, column=1, sticky='w')
        if receiver_mobile:
            ttk.Label(info, text="手机号:").grid(row=3, column=0, sticky='w')
            ttk.Label(info, text=receiver_mobile).grid(row=3, column=1, sticky='w')

        # 用户与金额（仅展示）
        ua = ttk.LabelFrame(main, text="用户/金额", padding="6")
        ua.pack(fill='x', pady=(0, 8))
        ttk.Label(ua, text="用户ID:").grid(row=0, column=0, sticky='w')
        ttk.Label(ua, text=str(user_id)).grid(row=0, column=1, sticky='w')
        ttk.Label(ua, text="用户信息:").grid(row=1, column=0, sticky='w')
        ttk.Label(ua, text=str(user_type)).grid(row=1, column=1, sticky='w')
        ttk.Label(ua, text="充值金额:").grid(row=2, column=0, sticky='w')
        ttk.Label(ua, text=f"{estimated_recharge}元").grid(row=2, column=1, sticky='w')

        # 按钮区
        btns = ttk.Frame(main)
        btns.pack(fill='x')

        def on_confirm():
            # 直接使用传入的 user_id 与 estimated_recharge
            if not str(user_id).isdigit():
                messagebox.showerror("错误", "用户ID格式不正确")
                return
            try:
                amt = float(estimated_recharge)
                if amt <= 0:
                    messagebox.showerror("错误", "充值金额必须大于0")
                    return
                if amt > 10000:
                    if not messagebox.askyesno("确认", f"充值金额 {amt} 元较大，确认继续？"):
                        return
            except ValueError:
                messagebox.showerror("错误", "充值金额格式不正确")
                return
            dialog.destroy()
            self.execute_recharge_for_order(str(user_id), float(estimated_recharge), order_no)

        def on_cancel():
            dialog.destroy()

        confirm_btn = ttk.Button(btns, text="确认", command=on_confirm)
        cancel_btn = ttk.Button(btns, text="取消", command=on_cancel)
        cancel_btn.pack(side='right')
        confirm_btn.pack(side='right', padx=(6, 0))

        # 居中（相对主窗口，兼容多显示器）
        def finalize():
            dialog.update_idletasks()
            try:
                parent = self.master
                parent.update_idletasks()
                px, py = parent.winfo_rootx(), parent.winfo_rooty()
                pw, ph = parent.winfo_width(), parent.winfo_height()
                dw, dh = dialog.winfo_width(), dialog.winfo_height()
                x = px + max((pw - dw) // 2, 0)
                y = py + max((ph - dh) // 2, 0)
            except Exception:
                # 回退到屏幕居中
                x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
                y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
            dialog.geometry(f"+{x}+{y}")
            confirm_btn.focus_set()
        dialog.after(0, finalize)

if __name__ == "__main__":
    root = tk.Tk()
    app = ManualRechargeApp(root)
    root.mainloop()
