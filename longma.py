import requests
import time
import hashlib
import json
import logging
import redis
import configparser
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from queue import Queue
from logging.handlers import RotatingFileHandler
from datetime import datetime, timedelta

# ========== 读取配置文件 ==========
config = configparser.ConfigParser()
config.read('config.ini', encoding='utf-8')

# ========== 从配置文件读取各种参数 ==========
APP_ID = config['Credentials']['APP_ID']
APP_SECRET = config['Credentials']['APP_SECRET']
SEND_MSG_TOKEN = config['Credentials']['SEND_MSG_TOKEN']

# 登录配置
USERNAME = config['Login']['username']
PASSWORD = config['Login']['password']
TENANT_ID = config['Login']['tenant_id']

# 基础URL和消息模板
BASE_URL = config['Settings']['BASE_URL']
ORDER_BASE_URL = 'https://open.goofish.pro'
MESSAGE_TEMPLATE = config['Settings']['message_template']

# 加价金额，默认值可以从配置文件中读取
EXTRA_CHARGE = float(config['Settings'].get('extra_charge', '1.08'))

# 订单金额最低要求，从配置文件读取
MIN_ORDER_AMOUNT = float(config['Settings'].get('min_order_amount', '4.2'))

# Redis连接配置，从配置文件读取
redis_host = config['Redis'].get('host', '127.0.0.1')
redis_port = int(config['Redis'].get('port', '6379'))
redis_db = int(config['Redis'].get('db', '0'))
redis_password = config['Redis'].get('password', '')

# 从配置文件读取用户类型判断配置
NEW_USER_MAX_ORDERS = int(config.get('UserType', 'new_user_max_orders', fallback='0'))
OLD_USER_MIN_ORDERS = int(config.get('UserType', 'old_user_min_orders', fallback='3'))
OLD_USER_MESSAGE = config.get('UserType', 'old_user_message', fallback='您不是首单了，需要补1元。首单亏本的')

# ========== 日志配置 ==========
logger = logging.getLogger()
logger.setLevel(logging.DEBUG)

formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler = RotatingFileHandler('app.log', maxBytes=5*1024*1024, backupCount=5, encoding='utf-8')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

# ========== 停止事件，用于停止线程 ==========
stop_event = threading.Event()

# ========== 全局令牌缓存 ==========
_cached_token = None
_token_expire_time = None

# ========== 连接Redis，带重试机制 ==========
def connect_redis(retries=5, delay=5):
    for i in range(retries):
        try:
            r = redis.Redis(host=redis_host, port=redis_port, db=redis_db, password=redis_password, socket_timeout=10)
            r.ping()
            return r
        except redis.exceptions.RedisError as e:
            logging.error(f"连接Redis失败，重试({i+1}/{retries}): {e}")
            time.sleep(delay)
    raise Exception("无法连接到Redis，请检查配置和网络。")

redis_client = connect_redis()

# ========== 工具函数 ==========
def md5(text):
    m = hashlib.md5()
    m.update(text.encode("utf8"))
    return m.hexdigest()

def gen_sign(body_json, timestamp, app_id, app_secret):
    """
    生成签名
    """
    body_md5 = md5(body_json)
    sign_str = f"{app_id},{body_md5},{timestamp},{app_secret}"
    logging.debug(f"生成的签名字符串: {sign_str}")
    return md5(sign_str)

def save_to_file(data, status):
    """
    保存订单信息到文件
    """
    formatted_string = (
        f"订单号----{data['order_no']}----实付金额:{data['pay_amount']}元"
        f"----手机号:{data['mobile']}----会员名:{data['buyer_nick']}----最终充值金额:{data['final_recharge_amount']}----状态:{status}"
    )
    with open("../充值记录.txt", "a", encoding='utf-8') as file:
        file.write(formatted_string + "\n")

def stable_request(method, url, **kwargs):
    """
    带有重试与超时机制的请求函数
    """
    retries = kwargs.pop('retries', 3)
    backoff_factor = kwargs.pop('backoff_factor', 2)
    timeout = kwargs.pop('timeout', 10)
    for attempt in range(retries):
        if stop_event.is_set():
            return None
        try:
            response = requests.request(method, url, timeout=timeout, **kwargs)
            if response.status_code == 401:
                return response
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logging.warning(f"请求失败，第{attempt+1}次重试，URL: {url}, 错误: {e}")
            time.sleep(backoff_factor ** attempt)
    logging.error(f"请求失败，已重试{retries}次仍不成功, URL: {url}")
    return None

def get_access_token(force_refresh=False):
    """
    获取访问令牌，支持缓存和按需刷新
    """
    global _cached_token, _token_expire_time

    # 检查是否有有效的缓存令牌
    if not force_refresh and _cached_token and _token_expire_time:
        if time.time() < _token_expire_time:
            logging.debug(f"使用缓存的Token: {_cached_token[:20]}...")
            return _cached_token

    # 获取新令牌
    url = f"{BASE_URL}/admin-api/system/auth/login"
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'Tenant-Id': TENANT_ID
    }
    data = json.dumps({"username": USERNAME, "password": PASSWORD})
    response = stable_request('post', url, headers=headers, data=data)

    if response and response.status_code == 200:
        try:
            response_data = response.json()
            token = response_data['data']['accessToken']

            # 缓存令牌，设置过期时间（默认1小时）
            _cached_token = token
            _token_expire_time = time.time() + 3600  # 1小时后过期

            logging.info(f"登录成功，获取到新Token: {token[:20]}...")
            return token
        except (KeyError, TypeError) as e:
            logging.error(f"解析Token响应失败: {e}")
            return None
    else:
        logging.error(f"登录失败，状态码: {response.status_code if response else 'None'}")
        return None

def refresh_token_if_needed(token):
    """
    检查令牌是否需要刷新（兼容旧代码）
    """
    if not token:
        return get_access_token()
    return token

def handle_token_error():
    """
    处理令牌错误，强制刷新令牌
    """
    global _cached_token, _token_expire_time
    logging.warning("检测到令牌失效，正在刷新...")
    _cached_token = None
    _token_expire_time = None
    return get_access_token(force_refresh=True)

def get_user_info(user_id, token):
    """
    根据用户ID查询用户信息 - 使用精确查询
    """
    try:
        # 使用分页接口的id参数进行精确查询
        user_url = f"{BASE_URL}/admin-api/system/mbr/user/page?pageNo=1&pageSize=10&deleted=0&id={user_id}&tenantId={TENANT_ID}"
        headers = {
            'Authorization': f'Bearer {token}',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Referer': f'{BASE_URL}/mbr/users',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
            'agent-tenant-id': str(TENANT_ID),
            'login_platform': 'web',
            'tenant-id': str(TENANT_ID)  # 修复：使用正确的租户ID 157
        }

        logging.info(f"查询用户信息: URL={user_url}")

        response = stable_request('get', user_url, headers=headers)

        if response and response.status_code == 401:
            # 令牌失效，尝试刷新
            logging.warning("用户查询令牌失效，尝试刷新")
            new_token = handle_token_error()
            if new_token:
                headers['Authorization'] = f'Bearer {new_token}'
                response = stable_request('get', user_url, headers=headers)

        if response and response.status_code == 200:
            try:
                response_data = response.json()
                logging.info(f"用户查询响应: {response_data}")

                if response_data.get('code') == 0:
                    users = response_data.get('data', {}).get('list', [])
                    total = response_data.get('data', {}).get('total', 0)

                    logging.info(f"查询结果: 总数={total}, 返回用户数={len(users)}")

                    if users and len(users) > 0:
                        user = users[0]  # 精确查询应该只返回一个用户
                        logging.info(f"查询用户信息成功: 用户ID {user_id}, 手机号: {user.get('mobile', 'N/A')}, 订单数: {user.get('channelNum', 0)}")
                        return user
                    else:
                        logging.warning(f"未找到用户ID为 {user_id} 的用户")
                        return None
                else:
                    error_msg = response_data.get('msg', '未知错误')
                    logging.error(f"查询用户信息失败: {error_msg}")
                    return None

            except (KeyError, TypeError) as e:
                logging.error(f"解析用户查询响应失败: {e}")
                return None
        else:
            error_msg = f"查询用户信息失败，状态码: {response.status_code if response else 'None'}"
            if response:
                try:
                    error_detail = response.json()
                    logging.error(f"{error_msg}, 响应内容: {error_detail}")
                except:
                    logging.error(f"{error_msg}, 响应文本: {response.text}")
            else:
                logging.error(error_msg)
            return None

    except Exception as e:
        logging.error(f"查询用户信息异常: {e}", exc_info=True)
        return None

def determine_user_type(user_info):
    """
    判断用户类型（新用户/老用户）- 使用配置文件设置
    """
    if not user_info:
        return "未知用户"

    # 从API响应中获取字段，支持多种字段名
    channel_num = user_info.get('channelNum', 0) or user_info.get('orderNum', 0) or 0
    balance = user_info.get('balance', 0) or 0

    # 根据配置文件中的设置判断用户类型
    # 新增逻辑：余额不为0的也视为老用户
    if balance != 0:
        return "老用户"
    elif channel_num <= NEW_USER_MAX_ORDERS and balance == 0:
        return "新用户"
    elif channel_num >= OLD_USER_MIN_ORDERS:
        return "老用户"
    else:
        return f"用户({channel_num}单)"



def is_user_blacklisted(mobile):
    """
    检查用户是否在黑名单中
    """
    try:
        return redis_client.sismember("recharged_users_blacklist", mobile)
    except redis.exceptions.RedisError as e:
        logging.error(f"查询黑名单失败: {e}")
        return False

def add_user_to_blacklist(mobile):
    """
    将用户加入黑名单
    """
    try:
        redis_client.sadd("recharged_users_blacklist", mobile)
    except redis.exceptions.RedisError as e:
        logging.error(f"添加黑名单失败: {e}")

def send_message(order_no, user_id, final_recharge_amount, custom_message=None):
    """
    发送消息通知
    """
    url = "https://aldsidle.agiso.com/api/Workbench/SendMsg"
    headers = {
        'User-Agent': ("Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 "
                       "(KHTML, like Gecko) IdleWorkbench/1.4.2 Chrome/106.0.5249.199 "
                       "Electron/21.4.4 Safari/537.36"),
        'Accept': "application/json, text/plain, */*",
        'Authorization': f"Bearer {SEND_MSG_TOKEN}",
        'X-Requested-With': "XMLHttpRequest",
        'Content-Type': "application/json",
        'Origin': "https://aldsidle.agiso.com",
    }

    # 如果有自定义消息，直接发送
    if custom_message:
        payload = json.dumps({"text": custom_message, "tid": order_no})
        response = stable_request('post', url, headers=headers, data=payload)
        if response and response.status_code == 200:
            logging.info("自定义消息发送成功: %s", custom_message)
        else:
            error_msg = f"自定义消息发送失败: {custom_message}"
            if response:
                error_msg += f" (状态码: {response.status_code})"
                if response.status_code == 401:
                    error_msg += " - Token可能已过期，请检查send_msg_token配置"
            logging.error(error_msg)
        return

    # 从配置文件读取消息内容并替换变量
    messages = []
    for i in range(1, 4):  # 读取message_1到message_3
        msg = config.get('Messages', f'message_{i}', fallback='')
        if msg:
            # 替换消息中的变量
            msg = msg.format(
                user_id=user_id,
                final_recharge_amount=final_recharge_amount
            )
            messages.append(msg)

    # 发送每条消息
    for message in messages:
        payload = json.dumps({"text": message, "tid": order_no})
        response = stable_request('post', url, headers=headers, data=payload)
        if response and response.status_code == 200:
            logging.info("消息发送成功: %s", message)
        else:
            error_msg = f"消息发送失败: {message}"
            if response:
                error_msg += f" (状态码: {response.status_code})"
                if response.status_code == 401:
                    error_msg += " - Token可能已过期，请检查send_msg_token配置"
            logging.error(error_msg)

def get_order_by_order_no(order_no, token):
    """
    根据订单号查询订单信息
    """
    try:
        current_timestamp = int(time.time())
        # 查询最近30天的订单
        thirty_days_ago = current_timestamp - 30 * 24 * 3600
        body_data = {
            "order_status": 12,
            "pay_time": [thirty_days_ago, current_timestamp],
            "order_no": order_no
        }
        body_json = json.dumps(body_data, separators=(',', ':'))
        sign = gen_sign(body_json, current_timestamp, APP_ID, APP_SECRET)
        order_url = f"{ORDER_BASE_URL}/api/open/order/list?appid={APP_ID}&timestamp={current_timestamp}&sign={sign}"

        order_response = stable_request(
            'post',
            order_url,
            headers={"Content-Type": "application/json", "Authorization": f'Bearer {token}'},
            data=body_json
        )

        if order_response and order_response.status_code == 200:
            response_data = order_response.json()
            logging.debug(f"订单查询API响应: {response_data}")
            orders = response_data.get('data', {}).get('list', [])
            logging.info(f"查询到 {len(orders)} 个订单")

            for order in orders:
                if order['order_no'] == order_no:
                    logging.info(f"找到匹配订单: {order_no}, 支付金额: {order.get('pay_amount', 'N/A')}")
                    return order

            logging.warning(f"未找到订单号为 {order_no} 的订单")
            return None
        else:
            error_msg = f"查询订单失败，状态码: {order_response.status_code if order_response else 'None'}"
            if order_response:
                try:
                    error_detail = order_response.json()
                    logging.error(f"{error_msg}, 响应内容: {error_detail}")
                except:
                    logging.error(error_msg)
            else:
                logging.error(error_msg)
            return None
    except Exception as e:
        logging.error(f"查询订单异常: {e}")
        return None

def manual_recharge_user(user_id, recharge_amount, order_no, token):
    """
    手动充值用户余额
    """
    try:
        recharge_url = f"{BASE_URL}/admin-api/kuaidi/mbr/user/recharge"
        recharge_data = {
            'mbrId': int(user_id),  # 确保用户ID是整数
            'balanceType': "1",
            'balance': str(recharge_amount),
            'changeReason': "1",
            'bizType': "balanceRecharge",
            'remark': order_no
        }

        # 详细记录充值请求信息
        logging.info(f"手动充值请求: URL={recharge_url}")
        logging.info(f"手动充值数据: {recharge_data}")

        recharge_response = stable_request(
            'put',
            recharge_url,
            headers={'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'},
            json=recharge_data
        )

        # 详细记录响应信息
        if recharge_response:
            logging.info(f"手动充值响应状态码: {recharge_response.status_code}")
            try:
                response_data = recharge_response.json()
                logging.info(f"手动充值响应内容: {response_data}")
            except:
                logging.info(f"手动充值响应文本: {recharge_response.text}")
        else:
            logging.error("手动充值响应为空")

        # 处理令牌失效的情况
        if recharge_response and recharge_response.status_code == 401:
            logging.warning("充值请求令牌失效，尝试刷新令牌后重试")
            new_token = handle_token_error()
            if new_token:
                # 使用新令牌重试
                recharge_response = stable_request(
                    'put',
                    recharge_url,
                    headers={'Authorization': f'Bearer {new_token}', 'Content-Type': 'application/json'},
                    json=recharge_data
                )
                logging.info(f"重试充值响应状态码: {recharge_response.status_code if recharge_response else 'None'}")

        if recharge_response and recharge_response.status_code == 200:
            # 验证响应内容
            try:
                response_data = recharge_response.json()
                if response_data.get('code') == 0 or response_data.get('success') == True:
                    logging.info(f"手动充值成功: 用户ID {user_id}, 充值金额 {recharge_amount}元, 订单号 {order_no}")
                    return True, "充值成功"
                else:
                    error_msg = f"充值失败: {response_data.get('msg', '未知错误')}"
                    logging.error(error_msg)
                    return False, error_msg
            except:
                # 如果无法解析JSON，但状态码是200，认为成功
                logging.info(f"手动充值成功: 用户ID {user_id}, 充值金额 {recharge_amount}元, 订单号 {order_no}")
                return True, "充值成功"
        else:
            error_msg = f"充值失败，状态码: {recharge_response.status_code if recharge_response else 'None'}"
            if recharge_response:
                try:
                    error_detail = recharge_response.json().get('msg', '未知错误')
                    error_msg += f", 错误详情: {error_detail}"
                except:
                    error_msg += f", 响应内容: {recharge_response.text}"
            logging.error(error_msg)
            return False, error_msg
    except Exception as e:
        error_msg = f"充值异常: {e}"
        logging.error(error_msg, exc_info=True)
        return False, error_msg

def process_orders_and_users():
    """
    监控新用户和新订单，符合条件的用户自动充值并发送通知
    """
    token = get_access_token()
    if not token:
        logging.error("无法获取初始token，终止脚本")
        return

    while not stop_event.is_set():
        token = refresh_token_if_needed(token)
        if not token:
            logging.error("获取token失败，等待10秒后重试")
            time.sleep(10)
            continue

        try:
            ten_minutes_ago = int(time.time() * 1000) - 600000
            user_url = f"{BASE_URL}/admin-api/system/mbr/user/page?pageNo=1&pageSize=50&deleted=0&tenantId={TENANT_ID}"
            user_response = stable_request('get', user_url, headers={'Authorization': f'Bearer {token}'})
            if not user_response:
                time.sleep(10)
                continue

            if user_response.status_code == 401:
                token = get_access_token()
                time.sleep(10)
                continue

            users = user_response.json().get('data', {}).get('list', [])
            # 最近10分钟新增用户 且 余额为0
            recent_users = [u for u in users if u['createTime'] > ten_minutes_ago and u['balance'] == 0]

            for user in recent_users:
                if stop_event.is_set():
                    break

                mobile = user['mobile']
                channel_num = user.get('channelNum', 0) or 0

                # 如果已下过 >= 3 单，提示要补1元，不再执行充值
                if user['balance'] == 0 and channel_num >= 3:
                    send_message(user.get('id'), user.get('id'), None, custom_message="您不是首单了，需要补1元。首单亏本的")
                    logging.info("发送首单提示消息成功: 用户ID %s", user['id'])
                    continue

                # 如果没在黑名单
                if not is_user_blacklisted(mobile):
                    current_timestamp = int(time.time())
                    body_data = {
                        "order_status": 12,
                        "pay_time": [ten_minutes_ago // 1000, current_timestamp]
                    }
                    body_json = json.dumps(body_data, separators=(',', ':'))
                    sign = gen_sign(body_json, current_timestamp, APP_ID, APP_SECRET)
                    order_url = f"{ORDER_BASE_URL}/api/open/order/list?appid={APP_ID}&timestamp={current_timestamp}&sign={sign}"

                    order_response = stable_request(
                        'post',
                        order_url,
                        headers={"Content-Type": "application/json", "Authorization": f'Bearer {token}'},
                        data=body_json
                    )
                    if not order_response:
                        time.sleep(10)
                        continue

                    if order_response.status_code == 401:
                        token = get_access_token()
                        continue

                    orders = order_response.json().get('data', {}).get('list', [])
                    for order in orders:
                        if stop_event.is_set():
                            break
                        if order['receiver_mobile'] == mobile:
                            pay_amount_yuan = round(float(order['pay_amount']) / 100.0, 2)
                            # 如果订单金额满足最低要求，则执行充值
                            if pay_amount_yuan >= MIN_ORDER_AMOUNT:
                                final_recharge_amount = round(pay_amount_yuan + EXTRA_CHARGE, 2)
                                # 当前用户余额=0，进行充值
                                if user['balance'] == 0:
                                    recharge_url = f"{BASE_URL}/admin-api/kuaidi/mbr/user/recharge"
                                    recharge_data = {
                                        'mbrId': user['id'],
                                        'balanceType': "1",
                                        'balance': str(final_recharge_amount),
                                        'changeReason': "1",
                                        'bizType': "balanceRecharge",
                                        'remark': order['order_no']
                                    }
                                    recharge_response = stable_request(
                                        'put',
                                        recharge_url,
                                        headers={'Authorization': f'Bearer {token}'},
                                        json=recharge_data
                                    )
                                    if recharge_response and recharge_response.status_code == 200:
                                        time.sleep(1)
                                        add_user_to_blacklist(mobile)
                                        send_message(order['order_no'], user['id'], final_recharge_amount)
                                        save_to_file({
                                            "order_no": order['order_no'],
                                            "pay_amount": pay_amount_yuan,
                                            "mobile": mobile,
                                            "buyer_nick": order['buyer_nick'],
                                            "final_recharge_amount": final_recharge_amount
                                        }, "成功")
                                    else:
                                        save_to_file({
                                            "order_no": order['order_no'],
                                            "pay_amount": pay_amount_yuan,
                                            "mobile": mobile,
                                            "buyer_nick": order['buyer_nick'],
                                            "final_recharge_amount": final_recharge_amount
                                        }, "充值失败")
                time.sleep(2)
        except Exception as e:
            logging.error(f"主循环发生错误: {e}", exc_info=True)
            time.sleep(10)

# ========== 日志队列处理，用于GUI实时显示日志 ==========
class QueueHandler(logging.Handler):
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        try:
            # 过滤掉不需要的DEBUG日志和手动充值相关的日志
            if (record.levelno >= logging.INFO and
                not self.is_manual_recharge_log(record) and
                not self.is_debug_noise(record)):
                msg = self.format(record)
                self.log_queue.put(msg)
        except Exception:
            self.handleError(record)

    def is_debug_noise(self, record):
        """判断是否为不需要显示的调试信息"""
        noise_keywords = [
            'Starting new HTTPS connection', 'https://', 'HTTP/1.1',
            'DEBUG', '生成的签名字符串'
        ]
        message = record.getMessage()
        return any(keyword in message for keyword in noise_keywords)

    def is_manual_recharge_log(self, record):
        """判断是否为手动充值相关的日志"""
        manual_keywords = [
            '手动充值', '订单查询API响应', '找到匹配订单', '未找到订单号',
            '查询到', '个订单', '开始手动充值', '使用自定义充值金额',
            '自动计算充值金额', '手动充值消息通知', '手动充值日志已清空'
        ]
        message = record.getMessage()
        return any(keyword in message for keyword in manual_keywords)

class ManualRechargeQueueHandler(logging.Handler):
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        try:
            # 只处理手动充值相关的重要日志
            if (record.levelno >= logging.INFO and
                self.is_manual_recharge_log(record) and
                not self.is_debug_noise(record)):
                msg = self.format(record)
                self.log_queue.put(msg)
        except Exception:
            self.handleError(record)

    def is_debug_noise(self, record):
        """判断是否为不需要显示的调试信息"""
        noise_keywords = [
            'Starting new HTTPS connection', 'https://', 'HTTP/1.1',
            'DEBUG', '生成的签名字符串', 'POST /api', 'GET /admin-api'
        ]
        message = record.getMessage()
        return any(keyword in message for keyword in noise_keywords)

    def is_manual_recharge_log(self, record):
        """判断是否为手动充值相关的日志"""
        manual_keywords = [
            '手动充值', '订单查询API响应', '找到匹配订单', '未找到订单号',
            '查询到', '个订单', '开始手动充值', '使用自定义充值金额',
            '自动计算充值金额', '手动充值消息通知', '手动充值日志已清空'
        ]
        message = record.getMessage()
        return any(keyword in message for keyword in manual_keywords)

# ========== GUI部分 ==========
class App:
    def __init__(self, master):
        self.master = master
        self.master.title("订单充值监控工具")
        self.master.geometry("1200x800")  # 增加宽度和高度以适应新布局

        self.style = ttk.Style()
        self.style.theme_use('default')

        # ========== 在此设置默认自动关闭时间 ==========
        # 默认：当日 22:30；若已过，则默认明日 22:30
        now = datetime.now()
        default_close_dt = datetime(now.year, now.month, now.day, 22, 30)
        if now >= default_close_dt:
            default_close_dt += timedelta(days=1)
        self.auto_close_time = default_close_dt  # 用于实际判断

        # ========== 手动充值功能区（移到最顶部）==========
        manual_recharge_main_frame = ttk.Labelframe(master, text="手动充值")
        manual_recharge_main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 创建左右分栏
        manual_content_frame = ttk.Frame(manual_recharge_main_frame)
        manual_content_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # 左侧：手动充值操作区
        manual_left_frame = ttk.Frame(manual_content_frame)
        manual_left_frame.pack(side='left', fill='y', padx=(0, 5))

        # 闲鱼订单号输入
        order_no_frame = ttk.Frame(manual_left_frame)
        order_no_frame.pack(fill='x', pady=5)
        ttk.Label(order_no_frame, text="闲鱼订单号*:", width=12).pack(side='left', padx=5)
        self.manual_order_no_var = tk.StringVar()
        self.manual_order_no_entry = ttk.Entry(order_no_frame, textvariable=self.manual_order_no_var, width=25)
        self.manual_order_no_entry.pack(side='left', padx=5)

        # 实收金额显示
        self.order_amount_var = tk.StringVar(value="")
        self.order_amount_label = ttk.Label(order_no_frame, textvariable=self.order_amount_var, foreground='green', font=('Arial', 9, 'bold'))
        self.order_amount_label.pack(side='left', padx=10)

        # 绑定订单号输入事件
        self.manual_order_no_var.trace_add('write', self.on_order_no_change)
        self.order_query_after_id = None  # 用于延迟查询的定时器ID

        # 用户ID输入
        user_id_frame = ttk.Frame(manual_left_frame)
        user_id_frame.pack(fill='x', pady=5)
        ttk.Label(user_id_frame, text="用户ID*:", width=12).pack(side='left', padx=5)
        self.manual_user_id_var = tk.StringVar()
        self.manual_user_id_entry = ttk.Entry(user_id_frame, textvariable=self.manual_user_id_var, width=20)
        self.manual_user_id_entry.pack(side='left', padx=5)

        # 用户类型显示
        self.user_type_var = tk.StringVar(value="")
        self.user_type_label = ttk.Label(user_id_frame, textvariable=self.user_type_var, foreground='blue', font=('Arial', 9, 'bold'))
        self.user_type_label.pack(side='left', padx=10)

        # 绑定用户ID输入事件
        self.manual_user_id_var.trace_add('write', self.on_user_id_change)
        self.user_query_after_id = None  # 用于延迟查询的定时器ID

        # 自定义充值金额输入
        custom_amount_frame = ttk.Frame(manual_left_frame)
        custom_amount_frame.pack(fill='x', pady=5)
        ttk.Label(custom_amount_frame, text="自定义金额:", width=12).pack(side='left', padx=5)
        self.manual_custom_amount_var = tk.StringVar()
        self.manual_custom_amount_entry = ttk.Entry(custom_amount_frame, textvariable=self.manual_custom_amount_var, width=15)
        self.manual_custom_amount_entry.pack(side='left', padx=5)
        ttk.Label(custom_amount_frame, text="(留空则自动计算)", font=('Arial', 8)).pack(side='left', padx=5)

        # 操作按钮和状态显示
        manual_btn_frame = ttk.Frame(manual_left_frame)
        manual_btn_frame.pack(fill='x', pady=10)
        self.manual_recharge_btn = ttk.Button(manual_btn_frame, text="执行充值", command=self.execute_manual_recharge)
        self.manual_recharge_btn.pack(side='left', padx=5)

        # 状态显示标签
        self.manual_status_var = tk.StringVar(value="就绪")
        self.manual_status_label = ttk.Label(manual_btn_frame, textvariable=self.manual_status_var, foreground='blue')
        self.manual_status_label.pack(side='left', padx=10)

        # 右侧：手动充值日志区
        manual_right_frame = ttk.Labelframe(manual_content_frame, text="手动充值日志")
        manual_right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))

        # 手动充值日志显示
        self.manual_log_text = scrolledtext.ScrolledText(manual_right_frame, wrap='word', height=12, width=50)
        self.manual_log_text.pack(fill='both', expand=True, padx=5, pady=5)

        # 日志清空按钮
        manual_log_btn_frame = ttk.Frame(manual_right_frame)
        manual_log_btn_frame.pack(fill='x', padx=5, pady=5)
        ttk.Button(manual_log_btn_frame, text="清空日志", command=self.clear_manual_log).pack(side='right')

        # 可折叠的配置区域
        self.config_visible = tk.BooleanVar(value=False)  # 默认隐藏
        config_header_frame = ttk.Frame(master)
        config_header_frame.pack(fill='x', padx=10, pady=2)

        self.config_toggle_btn = ttk.Button(config_header_frame, text="▶ 显示配置参数",
                                          command=self.toggle_config_visibility)
        self.config_toggle_btn.pack(side='left')

        self.config_frame = ttk.Frame(master)
        # 默认不显示

        # 配置内容
        config_content = ttk.Frame(self.config_frame)
        config_content.pack(fill='x', padx=10, pady=5)

        ttk.Label(config_content, text=f"BASE_URL: {BASE_URL}").pack(anchor='w', pady=2)
        ttk.Label(config_content, text=f"APP_ID: {APP_ID}").pack(anchor='w', pady=2)
        ttk.Label(config_content, text=f"Tenant_ID: {TENANT_ID}").pack(anchor='w', pady=2)
        ttk.Label(config_content, text="如需更改参数，请在config.ini中调整").pack(anchor='w', pady=2)

        # 添加send_msg_token设置
        token_frame = ttk.Frame(self.config_frame)
        token_frame.pack(fill='x', pady=5)
        ttk.Label(token_frame, text="消息Token: ").pack(side='left')
        self.token_var = tk.StringVar(value=SEND_MSG_TOKEN)
        self.token_entry = ttk.Entry(token_frame, textvariable=self.token_var, width=60)
        self.token_entry.pack(side='left', padx=5)
        self.update_token_btn = ttk.Button(token_frame, text="更新", command=self.update_send_msg_token)
        self.update_token_btn.pack(side='left', padx=5)

        extra_charge_frame = ttk.Frame(self.config_frame)
        extra_charge_frame.pack(fill='x', pady=5)
        ttk.Label(extra_charge_frame, text="加价金额: ").pack(side='left')
        self.extra_charge_var = tk.StringVar(value=str(EXTRA_CHARGE))
        self.extra_charge_entry = ttk.Entry(extra_charge_frame, textvariable=self.extra_charge_var, width=10)
        self.extra_charge_entry.pack(side='left', padx=5)
        self.update_extra_charge_btn = ttk.Button(extra_charge_frame, text="更新", command=self.update_extra_charge)
        self.update_extra_charge_btn.pack(side='left', padx=5)

        # 添加最低订单金额设置
        min_order_frame = ttk.Frame(self.config_frame)
        min_order_frame.pack(fill='x', pady=5)
        ttk.Label(min_order_frame, text="最低订单金额: ").pack(side='left')
        self.min_order_var = tk.StringVar(value=str(MIN_ORDER_AMOUNT))
        self.min_order_entry = ttk.Entry(min_order_frame, textvariable=self.min_order_var, width=10)
        self.min_order_entry.pack(side='left', padx=5)
        self.update_min_order_btn = ttk.Button(min_order_frame, text="更新", command=self.update_min_order_amount)
        self.update_min_order_btn.pack(side='left', padx=5)

        # 添加用户类型判断设置
        user_type_label_frame = ttk.Frame(self.config_frame)
        user_type_label_frame.pack(fill='x', pady=5)
        ttk.Label(user_type_label_frame, text="用户类型判断设置", font=('Arial', 10, 'bold')).pack(anchor='w')

        # 新用户最大订单数设置
        new_user_frame = ttk.Frame(self.config_frame)
        new_user_frame.pack(fill='x', pady=2)
        ttk.Label(new_user_frame, text="新用户最大订单数: ").pack(side='left')
        self.new_user_max_orders_var = tk.StringVar(value=str(NEW_USER_MAX_ORDERS))
        self.new_user_max_orders_entry = ttk.Entry(new_user_frame, textvariable=self.new_user_max_orders_var, width=5)
        self.new_user_max_orders_entry.pack(side='left', padx=5)
        self.update_new_user_btn = ttk.Button(new_user_frame, text="更新", command=self.update_new_user_max_orders)
        self.update_new_user_btn.pack(side='left', padx=5)
        ttk.Label(new_user_frame, text="(≤此值且余额为0判定为新用户)", font=('Arial', 8)).pack(side='left', padx=5)

        # 老用户最小订单数设置
        old_user_frame = ttk.Frame(self.config_frame)
        old_user_frame.pack(fill='x', pady=2)
        ttk.Label(old_user_frame, text="老用户最小订单数: ").pack(side='left')
        self.old_user_min_orders_var = tk.StringVar(value=str(OLD_USER_MIN_ORDERS))
        self.old_user_min_orders_entry = ttk.Entry(old_user_frame, textvariable=self.old_user_min_orders_var, width=5)
        self.old_user_min_orders_entry.pack(side='left', padx=5)
        self.update_old_user_btn = ttk.Button(old_user_frame, text="更新", command=self.update_old_user_min_orders)
        self.update_old_user_btn.pack(side='left', padx=5)
        ttk.Label(old_user_frame, text="(≥此值或余额≠0判定为老用户)", font=('Arial', 8)).pack(side='left', padx=5)

        # 老用户消息设置
        old_user_msg_frame = ttk.Frame(self.config_frame)
        old_user_msg_frame.pack(fill='x', pady=2)
        ttk.Label(old_user_msg_frame, text="老用户自动消息: ").pack(side='left')
        self.old_user_message_var = tk.StringVar(value=OLD_USER_MESSAGE)
        self.old_user_message_entry = ttk.Entry(old_user_msg_frame, textvariable=self.old_user_message_var, width=40)
        self.old_user_message_entry.pack(side='left', padx=5)
        self.update_old_user_msg_btn = ttk.Button(old_user_msg_frame, text="更新", command=self.update_old_user_message)
        self.update_old_user_msg_btn.pack(side='left', padx=5)
        ttk.Label(old_user_msg_frame, text="(识别为老用户时自动发送)", font=('Arial', 8)).pack(side='left', padx=5)

        # 可折叠的Redis配置区域
        self.redis_visible = tk.BooleanVar(value=False)  # 默认隐藏
        redis_header_frame = ttk.Frame(master)
        redis_header_frame.pack(fill='x', padx=10, pady=2)

        self.redis_toggle_btn = ttk.Button(redis_header_frame, text="▶ 显示Redis配置",
                                         command=self.toggle_redis_visibility)
        self.redis_toggle_btn.pack(side='left')

        self.redis_frame = ttk.Frame(master)
        # 默认不显示

        # Redis主机
        redis_host_frame = ttk.Frame(self.redis_frame)
        redis_host_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(redis_host_frame, text="Redis主机:").pack(side='left', padx=5)
        self.redis_host_var = tk.StringVar(value=redis_host)
        self.redis_host_entry = ttk.Entry(redis_host_frame, textvariable=self.redis_host_var, width=20)
        self.redis_host_entry.pack(side='left', padx=5)

        # Redis端口
        redis_port_frame = ttk.Frame(self.redis_frame)
        redis_port_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(redis_port_frame, text="Redis端口:").pack(side='left', padx=5)
        self.redis_port_var = tk.StringVar(value=str(redis_port))
        self.redis_port_entry = ttk.Entry(redis_port_frame, textvariable=self.redis_port_var, width=10)
        self.redis_port_entry.pack(side='left', padx=5)

        # Redis数据库
        redis_db_frame = ttk.Frame(self.redis_frame)
        redis_db_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(redis_db_frame, text="Redis数据库:").pack(side='left', padx=5)
        self.redis_db_var = tk.StringVar(value=str(redis_db))
        self.redis_db_entry = ttk.Entry(redis_db_frame, textvariable=self.redis_db_var, width=5)
        self.redis_db_entry.pack(side='left', padx=5)

        # Redis密码
        redis_pwd_frame = ttk.Frame(self.redis_frame)
        redis_pwd_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(redis_pwd_frame, text="Redis密码:").pack(side='left', padx=5)
        self.redis_pwd_var = tk.StringVar(value=redis_password)
        self.redis_pwd_entry = ttk.Entry(redis_pwd_frame, textvariable=self.redis_pwd_var, width=30)
        self.redis_pwd_entry.pack(side='left', padx=5)

        # Redis更新按钮
        redis_btn_frame = ttk.Frame(self.redis_frame)
        redis_btn_frame.pack(fill='x', padx=5, pady=5)
        self.update_redis_btn = ttk.Button(redis_btn_frame, text="更新Redis配置", command=self.update_redis_config)
        self.update_redis_btn.pack(side='left', padx=5)
        self.test_redis_btn = ttk.Button(redis_btn_frame, text="测试连接", command=self.test_redis_connection)
        self.test_redis_btn.pack(side='left', padx=5)

        # ========== 新增：消息设置功能区 ==========
        message_frame = ttk.Labelframe(master, text="消息设置")
        message_frame.pack(fill='x', padx=10, pady=5)

        # 消息1设置
        msg1_frame = ttk.Frame(message_frame)
        msg1_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(msg1_frame, text="消息1:").pack(side='left', padx=5)
        self.message1_var = tk.StringVar(value=config.get('Messages', 'message_1', fallback=''))
        self.message1_entry = ttk.Entry(msg1_frame, textvariable=self.message1_var, width=50)
        self.message1_entry.pack(side='left', padx=5)
        self.update_message1_btn = ttk.Button(msg1_frame, text="更新", command=lambda: self.update_message(1))
        self.update_message1_btn.pack(side='left', padx=5)

        # 消息2设置
        msg2_frame = ttk.Frame(message_frame)
        msg2_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(msg2_frame, text="消息2:").pack(side='left', padx=5)
        self.message2_var = tk.StringVar(value=config.get('Messages', 'message_2', fallback=''))
        self.message2_entry = ttk.Entry(msg2_frame, textvariable=self.message2_var, width=50)
        self.message2_entry.pack(side='left', padx=5)
        self.update_message2_btn = ttk.Button(msg2_frame, text="更新", command=lambda: self.update_message(2))
        self.update_message2_btn.pack(side='left', padx=5)

        # 消息3设置
        msg3_frame = ttk.Frame(message_frame)
        msg3_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(msg3_frame, text="消息3:").pack(side='left', padx=5)
        self.message3_var = tk.StringVar(value=config.get('Messages', 'message_3', fallback=''))
        self.message3_entry = ttk.Entry(msg3_frame, textvariable=self.message3_var, width=50)
        self.message3_entry.pack(side='left', padx=5)
        self.update_message3_btn = ttk.Button(msg3_frame, text="更新", command=lambda: self.update_message(3))
        self.update_message3_btn.pack(side='left', padx=5)

        # ========== 新增：定时自动关闭功能区 ==========
        auto_close_frame = ttk.Labelframe(master, text="自动关闭设置")
        auto_close_frame.pack(fill='x', padx=10, pady=5)

        # 时间输入说明
        time_label_frame = ttk.Frame(auto_close_frame)
        time_label_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(time_label_frame, text="自动关闭时间:").pack(side='left', padx=5)

        # 时间输入框
        time_input_frame = ttk.Frame(auto_close_frame)
        time_input_frame.pack(fill='x', padx=5, pady=2)

        # 日期输入
        ttk.Label(time_input_frame, text="日期:").pack(side='left', padx=5)
        self.auto_close_date_var = tk.StringVar(value=default_close_dt.strftime("%Y-%m-%d"))
        self.auto_close_date_entry = ttk.Entry(time_input_frame, textvariable=self.auto_close_date_var, width=12)
        self.auto_close_date_entry.pack(side='left', padx=5)

        # 时间输入
        ttk.Label(time_input_frame, text="时间:").pack(side='left', padx=5)
        default_time_str = default_close_dt.strftime("%H:%M")
        self.auto_close_time_var = tk.StringVar(value=default_time_str)
        self.auto_close_time_entry = ttk.Entry(time_input_frame, textvariable=self.auto_close_time_var, width=8)
        self.auto_close_time_entry.pack(side='left', padx=5)

        # 快捷按钮
        quick_btn_frame = ttk.Frame(auto_close_frame)
        quick_btn_frame.pack(fill='x', padx=5, pady=2)

        ttk.Button(quick_btn_frame, text="今日22:30", command=lambda: self.set_quick_time(0, "22:30")).pack(side='left', padx=2)
        ttk.Button(quick_btn_frame, text="明日22:30", command=lambda: self.set_quick_time(1, "22:30")).pack(side='left', padx=2)
        ttk.Button(quick_btn_frame, text="1小时后", command=self.set_one_hour_later).pack(side='left', padx=2)

        # 设置和状态按钮
        control_btn_frame = ttk.Frame(auto_close_frame)
        control_btn_frame.pack(fill='x', padx=5, pady=2)

        self.set_auto_close_btn = ttk.Button(control_btn_frame, text="设置自动关闭", command=self.set_auto_close_time)
        self.set_auto_close_btn.pack(side='left', padx=5)

        self.cancel_auto_close_btn = ttk.Button(control_btn_frame, text="取消自动关闭", command=self.cancel_auto_close)
        self.cancel_auto_close_btn.pack(side='left', padx=5)

        # 状态显示
        self.auto_close_status_var = tk.StringVar(value=f"已设置: {default_close_dt.strftime('%Y-%m-%d %H:%M')}")
        self.auto_close_status_label = ttk.Label(control_btn_frame, textvariable=self.auto_close_status_var, foreground='blue')
        self.auto_close_status_label.pack(side='left', padx=10)



        btn_frame = ttk.Frame(master)
        btn_frame.pack(fill='x', padx=10, pady=5)

        self.start_btn = ttk.Button(btn_frame, text="启动脚本", command=self.start_script)
        self.start_btn.pack(side='left', padx=5)

        self.stop_btn = ttk.Button(btn_frame, text="停止脚本", command=self.stop_script, state='disabled')
        self.stop_btn.pack(side='left', padx=5)

        # 日志输出
        log_frame = ttk.Labelframe(master, text="日志输出")
        log_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap='word')
        self.log_text.pack(fill='both', expand=True)

        # 自动充值日志队列
        self.log_queue = Queue()
        self.queue_handler = QueueHandler(self.log_queue)
        self.queue_handler.setLevel(logging.INFO)  # 只显示INFO及以上级别
        auto_fmt = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s',
                                   datefmt='%H:%M:%S')
        self.queue_handler.setFormatter(auto_fmt)
        logging.getLogger().addHandler(self.queue_handler)

        # 手动充值日志队列
        self.manual_log_queue = Queue()
        self.manual_queue_handler = ManualRechargeQueueHandler(self.manual_log_queue)
        self.manual_queue_handler.setLevel(logging.INFO)  # 只显示INFO及以上级别
        manual_fmt = logging.Formatter('%(asctime)s [手动] %(message)s',
                                     datefmt='%H:%M:%S')
        self.manual_queue_handler.setFormatter(manual_fmt)
        logging.getLogger().addHandler(self.manual_queue_handler)

        self.update_logs()

        ttk.Label(master, text="请确保config.ini和redis等配置正确后启动。", foreground='blue').pack(anchor='w', padx=10, pady=5)
        ttk.Label(master, text="您可以在上方配置区域修改配送服务名称和加价金额。", foreground='blue').pack(anchor='w', padx=10, pady=5)

        self.worker_thread = None

    # ============================ 日志实时更新 ============================
    def update_logs(self):
        # 更新自动充值日志
        while True:
            try:
                msg = self.log_queue.get_nowait()
            except Exception:
                break
            self.log_text.configure(state='normal')
            self.log_text.insert(tk.END, msg + '\n')
            self.log_text.configure(state='disabled')
            self.log_text.see(tk.END)

        # 更新手动充值日志
        while True:
            try:
                msg = self.manual_log_queue.get_nowait()
            except Exception:
                break
            self.manual_log_text.configure(state='normal')
            self.manual_log_text.insert(tk.END, msg + '\n')
            self.manual_log_text.configure(state='disabled')
            self.manual_log_text.see(tk.END)

        # 自动充值日志太多时，删除最早的多余行
        max_lines = 1000
        current_lines = int(self.log_text.index('end-1c').split('.')[0])
        if current_lines > max_lines:
            self.log_text.configure(state='normal')
            self.log_text.delete('1.0', f"{current_lines - max_lines}.0")
            self.log_text.configure(state='disabled')

        # 手动充值日志太多时，删除最早的多余行
        max_manual_lines = 500
        current_manual_lines = int(self.manual_log_text.index('end-1c').split('.')[0])
        if current_manual_lines > max_manual_lines:
            self.manual_log_text.configure(state='normal')
            self.manual_log_text.delete('1.0', f"{current_manual_lines - max_manual_lines}.0")
            self.manual_log_text.configure(state='disabled')

        # 定时检查是否到达自动关闭时间
        self.check_auto_close()

        self.master.after(1000, self.update_logs)

    # ============================ 自动关闭相关逻辑 ============================
    def set_auto_close_time(self):
        """
        设置自动关闭时间，支持未来任意时间
        """
        date_str = self.auto_close_date_var.get().strip()
        time_str = self.auto_close_time_var.get().strip()

        if not date_str or not time_str:
            messagebox.showerror("错误", "请输入完整的日期和时间")
            return

        try:
            # 解析日期
            date_parts = date_str.split('-')
            if len(date_parts) != 3:
                raise ValueError("日期格式错误")
            year, month, day = map(int, date_parts)

            # 解析时间
            time_parts = time_str.split(':')
            if len(time_parts) != 2:
                raise ValueError("时间格式错误")
            hour, minute = map(int, time_parts)

            # 创建目标时间
            new_dt = datetime(year, month, day, hour, minute)

        except ValueError as e:
            messagebox.showerror("错误", f"日期时间格式不正确：{e}\n请使用格式：YYYY-MM-DD HH:MM")
            return

        # 检查时间是否在未来
        now = datetime.now()
        if new_dt <= now:
            messagebox.showerror("错误", f"设置的时间已过期\n当前时间：{now.strftime('%Y-%m-%d %H:%M')}\n设置时间：{new_dt.strftime('%Y-%m-%d %H:%M')}")
            return

        self.auto_close_time = new_dt
        status_text = f"已设置: {new_dt.strftime('%Y-%m-%d %H:%M')}"
        self.auto_close_status_var.set(status_text)

        # 计算剩余时间
        time_diff = new_dt - now
        hours = int(time_diff.total_seconds() // 3600)
        minutes = int((time_diff.total_seconds() % 3600) // 60)

        logging.info(f"已设置自动关闭时间：{new_dt.strftime('%Y-%m-%d %H:%M')} (剩余 {hours}小时{minutes}分钟)")
        messagebox.showinfo("成功", f"已设置自动关闭时间：{new_dt.strftime('%Y-%m-%d %H:%M')}\n剩余时间：{hours}小时{minutes}分钟")

    def set_quick_time(self, days_offset, time_str):
        """
        快捷设置时间
        """
        target_date = datetime.now() + timedelta(days=days_offset)
        self.auto_close_date_var.set(target_date.strftime("%Y-%m-%d"))
        self.auto_close_time_var.set(time_str)

    def set_one_hour_later(self):
        """
        设置1小时后自动关闭
        """
        target_time = datetime.now() + timedelta(hours=1)
        self.auto_close_date_var.set(target_time.strftime("%Y-%m-%d"))
        self.auto_close_time_var.set(target_time.strftime("%H:%M"))

    def cancel_auto_close(self):
        """
        取消自动关闭
        """
        self.auto_close_time = None
        self.auto_close_status_var.set("已取消自动关闭")
        logging.info("已取消自动关闭设置")
        messagebox.showinfo("成功", "已取消自动关闭设置")

    def check_auto_close(self):
        """
        每秒检查是否到达自动关闭时间，并更新状态显示
        """
        if self.auto_close_time:
            now = datetime.now()
            time_diff = self.auto_close_time - now

            if time_diff.total_seconds() <= 0:
                # 时间到了，执行关闭
                logging.info("已到达自动关闭时间，正在强制关闭软件...")
                self.forcibly_stop_and_close()
            else:
                # 更新剩余时间显示
                hours = int(time_diff.total_seconds() // 3600)
                minutes = int((time_diff.total_seconds() % 3600) // 60)
                seconds = int(time_diff.total_seconds() % 60)

                if hours > 0:
                    remaining_text = f"剩余: {hours}小时{minutes}分钟"
                elif minutes > 0:
                    remaining_text = f"剩余: {minutes}分钟{seconds}秒"
                else:
                    remaining_text = f"剩余: {seconds}秒"

                status_text = f"已设置: {self.auto_close_time.strftime('%Y-%m-%d %H:%M')} ({remaining_text})"
                self.auto_close_status_var.set(status_text)

    def forcibly_stop_and_close(self):
        """
        不弹窗，直接停止脚本并关闭软件
        """
        if not stop_event.is_set():
            stop_event.set()
            logging.info("脚本停止中（自动关闭触发）...")
            self.stop_btn['state'] = 'disabled'

            def wait_thread():
                if self.worker_thread and self.worker_thread.is_alive():
                    self.master.after(300, wait_thread)
                else:
                    logging.info("脚本已停止，窗口即将关闭。")
                    self.start_btn['state'] = 'normal'
                    # 直接销毁窗口
                    self.master.destroy()

            wait_thread()

    # ============================ 启动 & 停止脚本 ============================
    def start_script(self):
        stop_event.clear()
        self.start_btn['state'] = 'disabled'
        self.stop_btn['state'] = 'normal'
        self.worker_thread = threading.Thread(target=process_orders_and_users)
        self.worker_thread.daemon = True
        self.worker_thread.start()
        logging.info("脚本已启动...")

    def stop_script(self):
        """
        手动停止脚本时，依旧询问用户是否确认
        如果想彻底取消弹窗，去掉 messagebox.askyesno 逻辑即可
        """
        if not stop_event.is_set():
            if messagebox.askyesno("确认", "确定要停止脚本执行吗？"):
                stop_event.set()
                logging.info("正在请求停止脚本...")
                self.stop_btn['state'] = 'disabled'

                def wait_thread():
                    if self.worker_thread and self.worker_thread.is_alive():
                        self.master.after(500, wait_thread)
                    else:
                        logging.info("脚本已停止。")
                        self.start_btn['state'] = 'normal'
                wait_thread()

    # ============================ 更新配置的功能 ============================
    def update_extra_charge(self):
        global EXTRA_CHARGE
        new_extra = self.extra_charge_var.get().strip()
        try:
            new_value = float(new_extra)
            EXTRA_CHARGE = new_value
            config.set('Settings', 'extra_charge', str(new_value))
            with open('config.ini', 'w', encoding='utf-8') as configfile:
                config.write(configfile)
            logging.info(f"加价金额已更新为: {new_value}")
            messagebox.showinfo("成功", f"加价金额已更新为: {new_value}")
        except ValueError:
            messagebox.showerror("错误", "请输入合法的数字。")

    def update_min_order_amount(self):
        """
        更新最低订单金额
        """
        global MIN_ORDER_AMOUNT
        new_min = self.min_order_var.get().strip()
        try:
            new_value = float(new_min)
            MIN_ORDER_AMOUNT = new_value
            config.set('Settings', 'min_order_amount', str(new_value))
            with open('config.ini', 'w', encoding='utf-8') as configfile:
                config.write(configfile)
            logging.info(f"最低订单金额已更新为: {new_value}")
            messagebox.showinfo("成功", f"最低订单金额已更新为: {new_value}")
        except ValueError:
            messagebox.showerror("错误", "请输入合法的数字。")

    def update_new_user_max_orders(self):
        """
        更新新用户最大订单数
        """
        global NEW_USER_MAX_ORDERS
        new_value_str = self.new_user_max_orders_var.get().strip()
        try:
            new_value = int(new_value_str)
            if new_value < 0:
                messagebox.showerror("错误", "订单数不能为负数")
                return

            # 确保UserType节存在
            if not config.has_section('UserType'):
                config.add_section('UserType')

            NEW_USER_MAX_ORDERS = new_value
            config.set('UserType', 'new_user_max_orders', str(new_value))
            with open('config.ini', 'w', encoding='utf-8') as configfile:
                config.write(configfile)

            logging.info(f"新用户最大订单数已更新为: {new_value}")
            messagebox.showinfo("成功", f"新用户最大订单数已更新为: {new_value}")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的整数")

    def update_old_user_min_orders(self):
        """
        更新老用户最小订单数
        """
        global OLD_USER_MIN_ORDERS
        new_value_str = self.old_user_min_orders_var.get().strip()
        try:
            new_value = int(new_value_str)
            if new_value < 0:
                messagebox.showerror("错误", "订单数不能为负数")
                return

            # 确保UserType节存在
            if not config.has_section('UserType'):
                config.add_section('UserType')

            OLD_USER_MIN_ORDERS = new_value
            config.set('UserType', 'old_user_min_orders', str(new_value))
            with open('config.ini', 'w', encoding='utf-8') as configfile:
                config.write(configfile)

            logging.info(f"老用户最小订单数已更新为: {new_value}")
            messagebox.showinfo("成功", f"老用户最小订单数已更新为: {new_value}")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的整数")

    def update_old_user_message(self):
        """
        更新老用户自动消息
        """
        global OLD_USER_MESSAGE
        new_message = self.old_user_message_var.get().strip()

        if not new_message:
            messagebox.showerror("错误", "消息内容不能为空")
            return

        # 确保UserType节存在
        if not config.has_section('UserType'):
            config.add_section('UserType')

        OLD_USER_MESSAGE = new_message
        config.set('UserType', 'old_user_message', new_message)
        with open('config.ini', 'w', encoding='utf-8') as configfile:
            config.write(configfile)

        logging.info(f"老用户自动消息已更新为: {new_message}")
        messagebox.showinfo("成功", f"老用户自动消息已更新为: {new_message}")

    def update_redis_config(self):
        """
        更新Redis配置
        """
        global redis_host, redis_port, redis_db, redis_password, redis_client

        # 获取新的配置值
        new_host = self.redis_host_var.get().strip()
        new_port_str = self.redis_port_var.get().strip()
        new_db_str = self.redis_db_var.get().strip()
        new_password = self.redis_pwd_var.get().strip()

        # 验证输入
        try:
            new_port = int(new_port_str)
            new_db = int(new_db_str)
        except ValueError:
            messagebox.showerror("错误", "端口和数据库必须是整数")
            return

        # 更新配置文件
        config.set('Redis', 'host', new_host)
        config.set('Redis', 'port', str(new_port))
        config.set('Redis', 'db', str(new_db))
        config.set('Redis', 'password', new_password)

        with open('config.ini', 'w', encoding='utf-8') as configfile:
            config.write(configfile)

        # 更新全局变量
        redis_host = new_host
        redis_port = new_port
        redis_db = new_db
        redis_password = new_password

        # 尝试重新连接Redis
        try:
            redis_client = connect_redis()
            logging.info("Redis配置已更新，并成功重新连接")
            messagebox.showinfo("成功", "Redis配置已更新，并成功重新连接")
        except Exception as e:
            logging.error(f"Redis配置已更新，但连接失败: {e}")
            messagebox.showerror("错误", f"Redis配置已更新，但连接失败: {e}")

    def test_redis_connection(self):
        """
        测试Redis连接
        """
        # 获取当前输入框中的配置
        test_host = self.redis_host_var.get().strip()
        test_port_str = self.redis_port_var.get().strip()
        test_db_str = self.redis_db_var.get().strip()
        test_password = self.redis_pwd_var.get().strip()

        # 验证输入
        try:
            test_port = int(test_port_str)
            test_db = int(test_db_str)
        except ValueError:
            messagebox.showerror("错误", "端口和数据库必须是整数")
            return

        # 尝试连接
        try:
            r = redis.Redis(
                host=test_host,
                port=test_port,
                db=test_db,
                password=test_password,
                socket_timeout=5
            )
            r.ping()
            logging.info("Redis连接测试成功")
            messagebox.showinfo("成功", "Redis连接测试成功")
        except redis.exceptions.RedisError as e:
            logging.error(f"Redis连接测试失败: {e}")
            messagebox.showerror("错误", f"Redis连接测试失败: {e}")

    # ============================ Token设置相关逻辑 ============================
    def update_send_msg_token(self):
        """
        更新消息发送Token
        """
        global SEND_MSG_TOKEN
        new_token = self.token_var.get().strip()
        if not new_token:
            messagebox.showerror("错误", "Token不能为空")
            return

        SEND_MSG_TOKEN = new_token
        config.set('Credentials', 'send_msg_token', new_token)
        with open('config.ini', 'w', encoding='utf-8') as configfile:
            config.write(configfile)
        logging.info("消息Token已更新")
        messagebox.showinfo("成功", "消息Token已更新")

    # ============================ 消息设置相关逻辑 ============================
    def update_message(self, msg_num):
        """
        更新指定编号的消息内容
        :param msg_num: 消息编号(1-3)
        """
        msg_var = getattr(self, f'message{msg_num}_var')
        new_message = msg_var.get().strip()
        if not new_message:
            messagebox.showerror("错误", f"消息{msg_num}内容不能为空。")
            return

        config.set('Messages', f'message_{msg_num}', new_message)
        with open('config.ini', 'w', encoding='utf-8') as configfile:
            config.write(configfile)
        logging.info(f"消息{msg_num}已更新为: {new_message}")
        messagebox.showinfo("成功", f"消息{msg_num}已更新为: {new_message}")

    # ============================ 手动充值相关逻辑 ============================
    def validate_manual_recharge_input(self):
        """
        验证手动充值输入参数
        """
        order_no = self.manual_order_no_var.get().strip()
        user_id = self.manual_user_id_var.get().strip()
        custom_amount = self.manual_custom_amount_var.get().strip()

        # 验证订单号
        if not order_no:
            return False, "请输入闲鱼订单号"

        # 验证用户ID
        if not user_id:
            return False, "请输入用户ID"

        try:
            user_id_int = int(user_id)
            if user_id_int <= 0:
                return False, "用户ID必须是正整数"
        except ValueError:
            return False, "用户ID格式不正确，必须是数字"

        # 验证自定义充值金额（如果有输入）
        if custom_amount:
            try:
                amount = float(custom_amount)
                if amount <= 0:
                    return False, "充值金额必须大于0"
                if amount > 10000:
                    return False, "充值金额不能超过10000元"
            except ValueError:
                return False, "充值金额格式不正确"

        return True, "验证通过"

    def execute_manual_recharge(self):
        """
        执行手动充值操作
        """
        # 验证输入
        is_valid, error_msg = self.validate_manual_recharge_input()
        if not is_valid:
            messagebox.showerror("输入错误", error_msg)
            return

        # 获取输入值
        user_id = self.manual_user_id_var.get().strip()
        custom_amount = self.manual_custom_amount_var.get().strip()

        # 检查用户类型（在主线程中进行）
        if not self._check_user_type_for_recharge(user_id, custom_amount):
            return

        # 禁用按钮，防止重复点击
        self.manual_recharge_btn['state'] = 'disabled'
        self.manual_status_var.set("处理中...")
        self.manual_status_label.config(foreground='orange')

        # 在新线程中执行充值操作
        threading.Thread(target=self._do_manual_recharge, daemon=True).start()

    def _check_user_type_for_recharge(self, user_id, custom_amount):
        """
        检查用户类型，对老用户进行特殊处理
        """
        try:
            # 获取访问令牌
            token = get_access_token()
            if not token:
                messagebox.showerror("错误", "获取访问令牌失败")
                return False

            # 查询用户信息
            user_info = get_user_info(user_id, token)

            if user_info:
                user_type = determine_user_type(user_info)
                channel_num = user_info.get('channelNum', 0) or user_info.get('orderNum', 0) or 0
                balance = user_info.get('balance', 0) or 0
                mobile = user_info.get('mobile', 'N/A')

                logging.info(f"充值前用户类型检查: {user_type}, 平台统计: {channel_num}, 余额: {balance}")

                # 如果是老用户，需要特殊处理
                if "老用户" in user_type:
                    # 检查是否填写了自定义金额
                    if not custom_amount:
                        messagebox.showerror("老用户充值限制",
                                           "老用户不支持自动识别金额充值，请在'自定义充值金额'框中手动填写充值金额。")
                        return False

                    # 弹出确认对话框
                    confirm_msg = f"""检测到老用户，请确认充值信息：

用户ID: {user_id}
手机号: {mobile}
平台统计: {channel_num}
当前余额: {balance}分
充值金额: {custom_amount}元

注意：老用户不支持自动识别金额充值，只能手动填写金额。

是否确认为该老用户充值？"""

                    result = messagebox.askyesno("老用户充值确认", confirm_msg)
                    logging.info(f"老用户充值确认结果: {result}")

                    if not result:
                        logging.info("用户取消老用户充值操作")
                        return False

            return True

        except Exception as e:
            logging.error(f"检查用户类型异常: {e}")
            messagebox.showerror("错误", f"检查用户类型失败: {e}")
            return False

    def _do_manual_recharge(self):
        """
        在后台线程中执行手动充值的具体逻辑
        """
        try:
            order_no = self.manual_order_no_var.get().strip()
            user_id = self.manual_user_id_var.get().strip()
            custom_amount = self.manual_custom_amount_var.get().strip()

            # 获取访问令牌
            token = get_access_token()
            if not token:
                self._update_manual_status("获取访问令牌失败", 'red', True)
                return

            # 查询订单信息
            logging.info(f"开始手动充值: 订单号 {order_no}, 用户ID {user_id}")
            order_info = get_order_by_order_no(order_no, token)

            if not order_info:
                self._update_manual_status("未找到指定订单或订单查询失败", 'red', True)
                return

            # 验证订单信息
            if 'pay_amount' not in order_info:
                self._update_manual_status("订单信息不完整，缺少支付金额", 'red', True)
                return

            # 计算充值金额
            if custom_amount:
                # 使用自定义金额
                final_recharge_amount = float(custom_amount)
                pay_amount_yuan = round(float(order_info['pay_amount']) / 100.0, 2)
                logging.info(f"使用自定义充值金额: {final_recharge_amount}元 (订单实付: {pay_amount_yuan}元)")
            else:
                # 自动计算金额
                pay_amount_yuan = round(float(order_info['pay_amount']) / 100.0, 2)
                final_recharge_amount = round(pay_amount_yuan + EXTRA_CHARGE, 2)
                logging.info(f"自动计算充值金额: 订单金额 {pay_amount_yuan}元 + 加价 {EXTRA_CHARGE}元 = {final_recharge_amount}元")

            # 执行充值
            success, result_msg = manual_recharge_user(user_id, final_recharge_amount, order_no, token)

            if success:
                # 充值成功，发送消息通知
                try:
                    send_message(order_no, user_id, final_recharge_amount)
                    logging.info("手动充值消息通知发送成功")
                except Exception as e:
                    logging.warning(f"手动充值消息通知发送失败: {e}")

                # 保存充值记录
                try:
                    save_to_file({
                        "order_no": order_no,
                        "pay_amount": round(float(order_info['pay_amount']) / 100.0, 2),
                        "mobile": order_info.get('receiver_mobile', '未知'),
                        "buyer_nick": order_info.get('buyer_nick', '未知'),
                        "final_recharge_amount": final_recharge_amount
                    }, "手动充值成功")
                except Exception as e:
                    logging.warning(f"保存充值记录失败: {e}")

                self._update_manual_status(f"充值成功！金额: {final_recharge_amount}元", 'green', True)
                # 清空输入框
                self.master.after(0, self._clear_manual_inputs)
            else:
                self._update_manual_status(f"充值失败: {result_msg}", 'red', True)

        except Exception as e:
            error_msg = f"操作异常: {str(e)}"
            logging.error(f"手动充值异常: {e}", exc_info=True)
            self._update_manual_status(error_msg, 'red', True)

    def _update_manual_status(self, message, color, enable_button):
        """
        更新手动充值状态显示
        """
        def update():
            self.manual_status_var.set(message)
            self.manual_status_label.config(foreground=color)
            if enable_button:
                self.manual_recharge_btn['state'] = 'normal'

        self.master.after(0, update)

    def _clear_manual_inputs(self):
        """
        清空手动充值输入框
        """
        self.manual_order_no_var.set("")
        self.manual_user_id_var.set("")
        self.manual_custom_amount_var.set("")
        self.order_amount_var.set("")
        self.user_type_var.set("")

    def clear_manual_log(self):
        """
        清空手动充值日志
        """
        self.manual_log_text.configure(state='normal')
        self.manual_log_text.delete('1.0', tk.END)
        self.manual_log_text.configure(state='disabled')
        logging.info("手动充值日志已清空")

    # ============================ 折叠/展开功能 ============================
    def toggle_config_visibility(self):
        """
        切换配置参数区域的显示/隐藏
        """
        if self.config_visible.get():
            # 当前显示，需要隐藏
            self.config_frame.pack_forget()
            self.config_toggle_btn.config(text="▶ 显示配置参数")
            self.config_visible.set(False)
        else:
            # 当前隐藏，需要显示
            # 找到config_toggle_btn的父容器，在其后插入config_frame
            config_header_frame = self.config_toggle_btn.master

            # 在config_header_frame之后插入config_frame
            self.config_frame.pack(fill='x', padx=10, pady=5, after=config_header_frame)
            self.config_toggle_btn.config(text="▼ 隐藏配置参数")
            self.config_visible.set(True)

    def toggle_redis_visibility(self):
        """
        切换Redis配置区域的显示/隐藏
        """
        if self.redis_visible.get():
            # 当前显示，需要隐藏
            self.redis_frame.pack_forget()
            self.redis_toggle_btn.config(text="▶ 显示Redis配置")
            self.redis_visible.set(False)
        else:
            # 当前隐藏，需要显示
            # 找到redis_toggle_btn的父容器，在其后插入redis_frame
            redis_header_frame = self.redis_toggle_btn.master

            # 在redis_header_frame之后插入redis_frame
            self.redis_frame.pack(fill='x', padx=10, pady=5, after=redis_header_frame)
            self.redis_toggle_btn.config(text="▼ 隐藏Redis配置")
            self.redis_visible.set(True)

    def on_order_no_change(self, *_):
        """
        订单号输入框内容变化时的回调函数
        """
        # 取消之前的延迟查询
        if self.order_query_after_id:
            self.master.after_cancel(self.order_query_after_id)

        # 清空之前的金额显示
        self.order_amount_var.set("")

        order_no = self.manual_order_no_var.get().strip()
        if len(order_no) >= 10:  # 订单号长度足够时才查询
            # 延迟1秒后查询，避免频繁查询
            self.order_query_after_id = self.master.after(1000, lambda: self.query_order_amount(order_no))

    def query_order_amount(self, order_no):
        """
        查询订单金额并显示
        """
        if not order_no or order_no != self.manual_order_no_var.get().strip():
            return  # 订单号已经改变，不需要查询

        # 在后台线程中查询
        threading.Thread(target=self._do_query_order_amount, args=(order_no,), daemon=True).start()

    def _do_query_order_amount(self, order_no):
        """
        在后台线程中执行订单金额查询
        """
        try:
            # 显示查询中状态
            self.master.after(0, lambda: self.order_amount_var.set("查询中..."))

            # 获取访问令牌
            token = get_access_token()
            if not token:
                self.master.after(0, lambda: self.order_amount_var.set("获取Token失败"))
                return

            # 查询订单信息
            order_info = get_order_by_order_no(order_no, token)

            if order_info and 'pay_amount' in order_info:
                pay_amount_yuan = round(float(order_info['pay_amount']) / 100.0, 2)
                # 计算预计充值金额
                estimated_recharge = round(pay_amount_yuan + EXTRA_CHARGE, 2)
                display_text = f"实收: {pay_amount_yuan}元 → 充值: {estimated_recharge}元"
                self.master.after(0, lambda: self.order_amount_var.set(display_text))
                logging.info(f"订单 {order_no} 实收金额: {pay_amount_yuan}元")
            else:
                self.master.after(0, lambda: self.order_amount_var.set("未找到订单"))

        except Exception as e:
            error_msg = "查询失败"
            logging.error(f"查询订单金额异常: {e}")
            self.master.after(0, lambda: self.order_amount_var.set(error_msg))

    def on_user_id_change(self, *_):
        """
        用户ID输入框内容变化时的回调函数
        """
        # 取消之前的延迟查询
        if self.user_query_after_id:
            self.master.after_cancel(self.user_query_after_id)

        # 清空之前的用户类型显示
        self.user_type_var.set("")

        user_id = self.manual_user_id_var.get().strip()
        if user_id and user_id.isdigit() and len(user_id) >= 3:  # 用户ID是数字且长度足够时才查询
            # 延迟1秒后查询，避免频繁查询
            self.user_query_after_id = self.master.after(1000, lambda: self.query_user_type(user_id))

    def query_user_type(self, user_id):
        """
        查询用户类型并显示
        """
        if not user_id or user_id != self.manual_user_id_var.get().strip():
            return  # 用户ID已经改变，不需要查询

        # 在后台线程中查询
        threading.Thread(target=self._do_query_user_type, args=(user_id,), daemon=True).start()

    def _do_query_user_type(self, user_id):
        """
        在后台线程中执行用户类型查询
        """
        try:
            # 显示查询中状态
            self.master.after(0, lambda: self.user_type_var.set("查询中..."))

            # 获取访问令牌
            token = get_access_token()
            if not token:
                self.master.after(0, lambda: self.user_type_var.set("获取Token失败"))
                return

            # 查询用户信息
            user_info = get_user_info(user_id, token)

            if user_info:
                user_type = determine_user_type(user_info)
                # 支持多种字段名
                channel_num = user_info.get('channelNum', 0) or user_info.get('orderNum', 0) or 0
                balance = user_info.get('balance', 0) or 0
                mobile = user_info.get('mobile', 'N/A')

                # 显示详细信息
                display_text = f"{user_type} (平台统计:{channel_num}, 余额:{balance})"
                self.master.after(0, lambda: self.user_type_var.set(display_text))
                logging.info(f"用户 {user_id} 类型: {user_type}, 平台统计: {channel_num}, 余额: {balance}, 手机: {mobile}")

                # 如果是老用户，自动发送消息
                if "老用户" in user_type:
                    self._send_old_user_message(user_id, mobile, channel_num, balance)
            else:
                self.master.after(0, lambda: self.user_type_var.set("用户不存在"))

        except Exception as e:
            error_msg = "查询失败"
            logging.error(f"查询用户类型异常: {e}")
            self.master.after(0, lambda: self.user_type_var.set(error_msg))

    def _send_old_user_message(self, user_id, mobile, channel_num, balance):
        """
        发送老用户消息
        """
        try:
            # 格式化消息内容，支持变量替换
            message_content = OLD_USER_MESSAGE.format(
                user_id=user_id,
                mobile=mobile,
                channel_num=channel_num,
                balance=balance
            )

            # 发送消息
            success = send_message("", user_id, 0, message_content)

            if success:
                logging.info(f"老用户消息发送成功: 用户ID {user_id}, 消息: {message_content}")
                # 在主线程中更新界面显示
                self.master.after(0, lambda: self._show_message_sent_status(True, message_content))
            else:
                logging.error(f"老用户消息发送失败: 用户ID {user_id}")
                self.master.after(0, lambda: self._show_message_sent_status(False, message_content))

        except Exception as e:
            logging.error(f"发送老用户消息异常: {e}")
            self.master.after(0, lambda: self._show_message_sent_status(False, "发送异常"))

    def _show_message_sent_status(self, success, message_content):
        """
        显示消息发送状态
        """
        if success:
            # 在用户类型显示后面添加发送状态
            current_text = self.user_type_var.get()
            if "✅已发送" not in current_text:
                self.user_type_var.set(f"{current_text} ✅已发送")

            # 显示发送的消息内容
            messagebox.showinfo("消息已发送", f"已向老用户发送消息:\n{message_content}")
        else:
            # 显示发送失败状态
            current_text = self.user_type_var.get()
            if "❌发送失败" not in current_text:
                self.user_type_var.set(f"{current_text} ❌发送失败")

            messagebox.showerror("发送失败", "老用户消息发送失败，请检查网络连接和配置")



# ========== 程序入口 ==========
if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()
