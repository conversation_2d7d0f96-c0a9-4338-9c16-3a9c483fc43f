#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from manual_recharge import get_all_orders, get_access_token, _orders_cache

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_cache_buyer_eid():
    """测试缓存中是否包含buyer_eid"""
    print("=== 测试缓存中的buyer_eid ===")
    
    try:
        # 获取Token
        token = get_access_token()
        if not token:
            print("❌ 无法获取访问令牌")
            return False
        print("✅ 成功获取访问令牌")
        
        # 清除缓存，强制重新获取
        import manual_recharge
        manual_recharge._orders_cache = {}
        print("✅ 已清除订单缓存")
        
        # 获取订单列表
        orders = get_all_orders(token, days=7)
        if not orders:
            print("❌ 无法获取订单列表")
            return False
        
        print(f"✅ 成功获取 {len(orders)} 个订单")
        
        # 检查前几个订单的buyer_eid
        print("\n=== 检查订单中的buyer_eid字段 ===")
        for i, order in enumerate(orders[:5], 1):  # 检查前5个订单
            order_no = order.get('order_no', 'N/A')
            buyer_eid = order.get('buyer_eid', 'N/A')
            buyer_nick = order.get('buyer_nick', 'N/A')
            pay_amount = order.get('pay_amount_yuan', 0)
            
            print(f"订单 {i}:")
            print(f"  订单号: {order_no}")
            print(f"  buyer_eid: {buyer_eid}")
            print(f"  买家昵称: {buyer_nick}")
            print(f"  金额: {pay_amount}元")
            print(f"  buyer_eid状态: {'✅ 存在' if buyer_eid and buyer_eid != 'N/A' else '❌ 缺失'}")
            print("-" * 40)
        
        # 检查缓存中的数据
        print("\n=== 检查缓存中的数据 ===")
        cached_orders = manual_recharge._orders_cache
        if cached_orders:
            print(f"✅ 缓存中有 {len(cached_orders)} 个订单")
            
            # 检查缓存中第一个订单的buyer_eid
            if cached_orders:
                first_cached = cached_orders[0]
                cached_buyer_eid = first_cached.get('buyer_eid', 'N/A')
                cached_order_no = first_cached.get('order_no', 'N/A')
                print(f"缓存中第一个订单:")
                print(f"  订单号: {cached_order_no}")
                print(f"  buyer_eid: {cached_buyer_eid}")
                print(f"  buyer_eid状态: {'✅ 存在' if cached_buyer_eid and cached_buyer_eid != 'N/A' else '❌ 缺失'}")
        else:
            print("❌ 缓存为空")
        
        # 统计有buyer_eid的订单数量
        orders_with_buyer_eid = 0
        orders_without_buyer_eid = 0
        
        for order in orders:
            buyer_eid = order.get('buyer_eid')
            if buyer_eid and buyer_eid.strip():
                orders_with_buyer_eid += 1
            else:
                orders_without_buyer_eid += 1
        
        print(f"\n=== 统计结果 ===")
        print(f"总订单数: {len(orders)}")
        print(f"有buyer_eid的订单: {orders_with_buyer_eid}")
        print(f"缺少buyer_eid的订单: {orders_without_buyer_eid}")
        print(f"buyer_eid覆盖率: {orders_with_buyer_eid/len(orders)*100:.1f}%")
        
        return orders_with_buyer_eid > 0
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        logging.error(f"测试缓存buyer_eid异常: {e}", exc_info=True)
        return False

def search_specific_order(order_no):
    """搜索特定订单号的buyer_eid"""
    print(f"\n=== 搜索订单 {order_no} ===")
    
    try:
        token = get_access_token()
        if not token:
            print("❌ 无法获取访问令牌")
            return None
        
        # 强制刷新缓存
        import manual_recharge
        manual_recharge._orders_cache = {}
        
        orders = get_all_orders(token, days=7)
        if not orders:
            print("❌ 无法获取订单列表")
            return None
        
        # 搜索指定订单
        for order in orders:
            if order.get('order_no') == order_no:
                buyer_eid = order.get('buyer_eid')
                buyer_nick = order.get('buyer_nick')
                pay_amount = order.get('pay_amount_yuan', 0)
                
                print(f"✅ 找到订单:")
                print(f"  订单号: {order_no}")
                print(f"  buyer_eid: {buyer_eid}")
                print(f"  买家昵称: {buyer_nick}")
                print(f"  金额: {pay_amount}元")
                print(f"  buyer_eid状态: {'✅ 存在' if buyer_eid and buyer_eid.strip() else '❌ 缺失'}")
                
                return buyer_eid
        
        print(f"❌ 未找到订单号 {order_no}")
        return None
        
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
        return None

if __name__ == "__main__":
    print("开始测试缓存中的buyer_eid...")
    
    # 测试缓存功能
    success = test_cache_buyer_eid()
    
    # 搜索特定订单
    target_order = "4683940419366546746"  # 从日志中看到的订单号
    buyer_eid = search_specific_order(target_order)
    
    print(f"\n{'='*50}")
    print("测试结果总结:")
    print(f"缓存buyer_eid测试: {'✅ 通过' if success else '❌ 失败'}")
    print(f"特定订单搜索: {'✅ 找到' if buyer_eid else '❌ 未找到'}")
    
    if success and buyer_eid:
        print("\n🎉 缓存中包含buyer_eid，问题应该已经解决！")
        print("现在进行充值操作应该能够正确保存buyer_eid到黑名单数据库。")
    else:
        print("\n⚠️ 仍有问题需要进一步排查。")
        print("请检查订单API返回的数据结构是否包含buyer_eid字段。")
