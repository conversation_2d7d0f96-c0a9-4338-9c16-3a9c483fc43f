# 手动充值工具

这是一个独立的手动充值工具，从原始的订单充值监控工具中提取出来，专门用于手动查询订单、用户信息并执行充值操作。

## 功能特性

- **订单列表**：自动拉取最近7天的所有订单，以表格形式展示订单详情
- **用户查询**：输入用户ID或通过订单自动查询用户类型（新用户/老用户）、订单数、余额等信息
- **批量充值**：表格中每个订单都可以直接充值，支持自定义充值金额或使用预计算金额
- **智能匹配**：选择订单后自动通过手机号查询对应的用户ID
- **老用户识别**：自动识别老用户类型并在充值前进行确认
- **实时日志**：显示详细的操作日志，便于跟踪充值过程
- **配置管理**：支持在界面中动态调整加价金额等参数

## 安装与运行

### 环境要求

- Python 3.7+
- Windows/Linux/macOS

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置设置

1. 复制 `manual_recharge_config.ini` 文件
2. 修改配置文件中的参数：
   - `[Credentials]` 部分：填入正确的 APP_ID、APP_SECRET、SEND_MSG_TOKEN
   - `[Login]` 部分：填入登录用户名、密码、租户ID
   - `[Settings]` 部分：设置基础URL、加价金额等
   - `[Messages]` 部分：自定义消息模板
   - `[UserType]` 部分：设置新老用户判断标准

### 运行程序

```bash
python manual_recharge.py
```

## 使用说明

### 基本操作流程

1. **刷新订单**：点击"刷新订单列表"按钮获取最近7天的订单数据
2. **选择订单**：在订单列表中双击要充值的订单，系统会自动：
   - 显示订单详情（订单号、实付金额、预计充值金额）
   - 通过收货手机号自动查询对应的用户ID
3. **确认用户**：检查自动填入的用户ID是否正确，如需要可手动修改
4. **设置充值金额**：
   - 留空"自定义金额"：使用预计算的充值金额（实收金额 + 加价金额）
   - 填入"自定义金额"：使用指定的充值金额
5. **执行充值**：点击"执行充值"按钮开始充值操作
6. **查看结果**：在右侧日志区域查看充值结果和详细信息

### 老用户处理

- 系统会自动识别老用户（订单数≥3或余额≠0）
- 对老用户会显示确认对话框，询问是否继续充值
- 确认后会自动发送老用户提醒消息

### 界面说明

- **左侧订单列表**：显示所有可充值的订单，包含订单号、实付金额、收货手机号、买家昵称、预计充值金额
- **右上角用户查询**：显示选中订单对应的用户信息
- **右中充值操作**：显示选中的订单信息和充值操作按钮
- **右下操作日志**：显示详细的充值操作记录

### 配置调整

- 可以在界面下方的"配置参数"区域查看当前配置
- 支持在线调整"加价金额"并保存到配置文件

## 文件说明

- `manual_recharge.py`：主程序文件
- `manual_recharge_config.ini`：配置文件
- `manual_recharge.log`：运行日志文件
- `manual_recharge_records.txt`：充值记录文件
- `requirements.txt`：Python依赖包列表
- `README.md`：说明文档

## 配置文件详解

### [Credentials] 凭据配置
- `app_id`：应用ID
- `app_secret`：应用密钥
- `send_msg_token`：消息发送Token

### [Login] 登录配置
- `username`：登录用户名
- `password`：登录密码
- `tenant_id`：租户ID

### [Settings] 基础设置
- `base_url`：API基础地址
- `extra_charge`：加价金额（元）
- `min_order_amount`：最低订单金额要求（元）

### [Messages] 消息模板
- `message_1`、`message_2`、`message_3`：充值成功后发送的消息模板
- 支持变量替换：`{user_id}`、`{final_recharge_amount}`

### [UserType] 用户类型判断
- `new_user_max_orders`：新用户最大订单数
- `old_user_min_orders`：老用户最小订单数
- `old_user_message`：老用户自动消息内容

## 注意事项

1. **配置安全**：配置文件包含敏感信息，请妥善保管，避免泄露
2. **网络连接**：确保网络连接正常，能够访问相关API接口
3. **权限验证**：确保登录账号有充值和查询权限
4. **数据备份**：重要操作前建议备份相关数据
5. **测试环境**：建议先在测试环境验证功能正常后再用于生产

## 常见问题

### Q: 提示"获取访问令牌失败"
A: 检查网络连接和登录配置（用户名、密码、租户ID）是否正确

### Q: 提示"未找到订单"
A: 确认订单号正确，且订单在最近30天内

### Q: 提示"用户不存在"
A: 确认用户ID正确，且用户在系统中存在

### Q: 消息发送失败
A: 检查 send_msg_token 配置是否正确，网络是否正常

## 技术支持

如有问题或建议，请联系技术支持团队。
