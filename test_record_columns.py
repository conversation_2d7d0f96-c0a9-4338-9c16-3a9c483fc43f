#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def test_record_columns():
    """测试充值记录表格的列结构"""
    
    # 模拟充值记录的列结构
    record_columns = ('time', 'order_no', 'buyer_nick', 'user_id', 'amount', 'type', 'status', 'reason', 'action')
    
    print("=== 充值记录表格列结构测试 ===")
    print(f"总列数: {len(record_columns)}")
    print()
    
    for i, column in enumerate(record_columns, 1):
        print(f"#{i} - {column}")
    
    print()
    print("=== 关键列索引 ===")
    print(f"失败原因列: #{record_columns.index('reason') + 1}")
    print(f"操作列: #{record_columns.index('action') + 1}")
    
    # 模拟一条充值记录
    sample_record = [
        "2025-08-14 18:30:15",  # time
        "123456789",            # order_no
        "小明",                 # buyer_nick
        "1001",                 # user_id
        "5.58",                 # amount
        "普通充值",             # type
        "成功",                 # status
        "",                     # reason (成功时为空)
        "扣除"                  # action
    ]
    
    print()
    print("=== 示例记录数据 ===")
    for i, value in enumerate(sample_record):
        column_name = record_columns[i] if i < len(record_columns) else f"extra_{i}"
        print(f"#{i+1} {column_name}: {value}")
    
    # 模拟失败记录
    failed_record = [
        "2025-08-14 18:31:20",  # time
        "987654321",            # order_no
        "小红",                 # buyer_nick
        "1002",                 # user_id
        "4.50",                 # amount
        "强制充值",             # type
        "失败",                 # status
        "用户不存在",           # reason
        "-"                     # action (失败时不能扣除)
    ]
    
    print()
    print("=== 失败记录示例 ===")
    for i, value in enumerate(failed_record):
        column_name = record_columns[i] if i < len(record_columns) else f"extra_{i}"
        print(f"#{i+1} {column_name}: {value}")
    
    print()
    print("=== 点击事件测试 ===")
    print("点击失败原因列 (#8) -> 不应该触发扣除操作")
    print("点击操作列 (#9) -> 应该触发扣除操作")
    
    return True

if __name__ == "__main__":
    test_record_columns()
    print("\n✅ 列结构测试完成！")
    print("现在点击失败原因列不会触发扣除操作了。")
