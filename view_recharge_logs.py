#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
from datetime import datetime, timedelta

def view_recent_logs(log_file='manual_recharge.log', minutes=10):
    """查看最近几分钟的日志"""
    if not os.path.exists(log_file):
        print(f"日志文件 {log_file} 不存在")
        return
    
    print(f"=== 查看最近 {minutes} 分钟的充值日志 ===")
    print(f"日志文件: {log_file}")
    print(f"查看时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 80)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 计算时间阈值
        now = datetime.now()
        time_threshold = now - timedelta(minutes=minutes)
        
        recent_lines = []
        for line in lines:
            try:
                # 提取日志时间戳 (格式: 2025-08-14 17:37:12,694)
                if len(line) > 23:
                    time_str = line[:23]
                    log_time = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S,%f')
                    if log_time >= time_threshold:
                        recent_lines.append(line.rstrip())
            except:
                # 如果解析时间失败，跳过这行
                continue
        
        if recent_lines:
            print(f"找到 {len(recent_lines)} 条最近的日志:")
            print()
            for i, line in enumerate(recent_lines, 1):
                print(f"{i:3d}: {line}")
        else:
            print(f"最近 {minutes} 分钟内没有日志记录")
            
    except Exception as e:
        print(f"读取日志文件失败: {e}")

def view_recharge_related_logs(log_file='manual_recharge.log', lines_count=50):
    """查看充值相关的日志"""
    if not os.path.exists(log_file):
        print(f"日志文件 {log_file} 不存在")
        return
    
    print(f"=== 查看最近的充值相关日志 ===")
    print(f"日志文件: {log_file}")
    print("-" * 80)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找充值相关的日志
        recharge_keywords = [
            '[充值流程]', '[强制充值流程]', '[普通充值]', '[强制充值]',
            '[充值API]', '[用户检查]', '充值成功', '充值失败',
            'buyer_eid', '黑名单', '手动充值'
        ]
        
        recharge_lines = []
        for line in lines[-lines_count*2:]:  # 检查最后的更多行以找到相关日志
            if any(keyword in line for keyword in recharge_keywords):
                recharge_lines.append(line.rstrip())
        
        if recharge_lines:
            print(f"找到 {len(recharge_lines)} 条充值相关日志:")
            print()
            for i, line in enumerate(recharge_lines[-lines_count:], 1):  # 只显示最后的指定数量
                print(f"{i:3d}: {line}")
        else:
            print("没有找到充值相关的日志")
            
    except Exception as e:
        print(f"读取日志文件失败: {e}")

def tail_logs(log_file='manual_recharge.log', follow=True):
    """实时跟踪日志文件（类似tail -f）"""
    if not os.path.exists(log_file):
        print(f"日志文件 {log_file} 不存在")
        return
    
    print(f"=== 实时跟踪日志文件 ===")
    print(f"日志文件: {log_file}")
    print("按 Ctrl+C 停止跟踪")
    print("-" * 80)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            # 移动到文件末尾
            f.seek(0, 2)
            
            while follow:
                line = f.readline()
                if line:
                    print(line.rstrip())
                else:
                    time.sleep(0.1)  # 等待新内容
                    
    except KeyboardInterrupt:
        print("\n停止跟踪日志")
    except Exception as e:
        print(f"跟踪日志失败: {e}")

def main():
    """主菜单"""
    while True:
        print("\n" + "="*60)
        print("充值日志查看工具")
        print("="*60)
        print("1. 查看最近10分钟的日志")
        print("2. 查看最近30分钟的日志")
        print("3. 查看最近的充值相关日志")
        print("4. 实时跟踪日志（类似tail -f）")
        print("5. 查看指定时间范围的日志")
        print("0. 退出")
        print("-"*60)
        
        choice = input("请选择操作 (0-5): ").strip()
        
        if choice == '1':
            view_recent_logs(minutes=10)
        elif choice == '2':
            view_recent_logs(minutes=30)
        elif choice == '3':
            view_recharge_related_logs()
        elif choice == '4':
            tail_logs()
        elif choice == '5':
            try:
                minutes = int(input("请输入要查看的分钟数: "))
                view_recent_logs(minutes=minutes)
            except ValueError:
                print("请输入有效的数字")
        elif choice == '0':
            print("退出日志查看工具")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    # 如果直接运行，显示最近的充值相关日志
    print("快速查看最近的充值相关日志:")
    view_recharge_related_logs()
    
    print("\n" + "="*60)
    print("提示: 运行 'python view_recharge_logs.py' 可以进入交互模式")
    print("="*60)
