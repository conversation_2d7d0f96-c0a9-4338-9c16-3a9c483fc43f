#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from manual_recharge import get_all_orders, get_access_token

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_current_orders_issue():
    """调试self.current_orders的问题"""
    print("=== 调试self.current_orders问题 ===")
    
    try:
        # 模拟充值流程中的订单获取
        token = get_access_token()
        if not token:
            print("❌ 无法获取访问令牌")
            return
        
        print("✅ 成功获取访问令牌")
        
        # 清除缓存，模拟充值时的刷新
        import manual_recharge
        manual_recharge._orders_cache = {}
        print("✅ 已清除订单缓存")
        
        # 获取订单列表（模拟充值时的刷新）
        print("\n📥 模拟充值时的订单刷新...")
        fresh_orders = get_all_orders(token, days=7)
        if not fresh_orders:
            print("❌ 无法获取订单列表")
            return
        
        print(f"✅ 获取到 {len(fresh_orders)} 个订单")
        
        # 检查目标订单是否在fresh_orders中
        target_order = "2878089314222615854"
        found_in_fresh = False
        
        print(f"\n🔍 检查目标订单 {target_order} 是否在fresh_orders中:")
        for order in fresh_orders:
            if order.get('order_no') == target_order:
                found_in_fresh = True
                buyer_eid = order.get('buyer_eid', '')
                buyer_nick = order.get('buyer_nick', 'N/A')
                pay_amount = order.get('pay_amount_yuan', 0)
                
                print(f"✅ 在fresh_orders中找到目标订单:")
                print(f"  订单号: {target_order}")
                print(f"  买家昵称: {buyer_nick}")
                print(f"  金额: {pay_amount}元")
                print(f"  buyer_eid: {buyer_eid}")
                break
        
        if not found_in_fresh:
            print(f"❌ 在fresh_orders中未找到目标订单 {target_order}")
            print("前10个订单号:")
            for i, order in enumerate(fresh_orders[:10], 1):
                print(f"  {i}. {order.get('order_no', 'N/A')}")
        
        # 模拟充值时的查找逻辑
        print(f"\n🔍 模拟充值时的查找逻辑:")
        print("模拟代码: for order in self.current_orders:")
        print("            if order.get('order_no') == order_no:")
        
        # 直接使用fresh_orders作为current_orders进行查找
        current_orders = fresh_orders  # 这就是self.current_orders应该的值
        
        order_info = None
        for order in current_orders:
            if order.get('order_no') == target_order:
                order_info = order
                break
        
        if order_info:
            buyer_eid = order_info.get('buyer_eid', '')
            buyer_nick = order_info.get('buyer_nick', 'N/A')
            pay_amount = order_info.get('pay_amount_yuan', 0)
            
            print(f"✅ 查找逻辑成功找到订单:")
            print(f"  订单号: {target_order}")
            print(f"  买家昵称: {buyer_nick}")
            print(f"  金额: {pay_amount}元")
            print(f"  buyer_eid: {buyer_eid}")
        else:
            print(f"❌ 查找逻辑未找到订单 {target_order}")
        
        # 检查订单号的数据类型
        print(f"\n🔍 检查订单号的数据类型:")
        if fresh_orders:
            first_order = fresh_orders[0]
            order_no_value = first_order.get('order_no')
            print(f"第一个订单号: {order_no_value}")
            print(f"订单号类型: {type(order_no_value)}")
            print(f"目标订单号: {target_order}")
            print(f"目标订单号类型: {type(target_order)}")
            
            # 检查是否有类型不匹配的问题
            if str(order_no_value) == str(target_order):
                print("✅ 字符串比较正常")
            else:
                print("❌ 字符串比较有问题")
        
        # 检查是否有重复订单号
        print(f"\n🔍 检查是否有重复订单号:")
        order_nos = [order.get('order_no') for order in fresh_orders]
        unique_order_nos = set(order_nos)
        
        print(f"总订单数: {len(order_nos)}")
        print(f"唯一订单号数: {len(unique_order_nos)}")
        
        if len(order_nos) != len(unique_order_nos):
            print("⚠️ 发现重复订单号")
            # 找出重复的订单号
            from collections import Counter
            counter = Counter(order_nos)
            duplicates = [order_no for order_no, count in counter.items() if count > 1]
            print(f"重复的订单号: {duplicates}")
        else:
            print("✅ 没有重复订单号")
        
        return found_in_fresh and order_info is not None
        
    except Exception as e:
        print(f"❌ 调试异常: {e}")
        logging.error(f"调试current_orders异常: {e}", exc_info=True)
        return False

def test_order_comparison():
    """测试订单号比较逻辑"""
    print(f"\n=== 测试订单号比较逻辑 ===")
    
    target_order = "2878089314222615854"
    
    # 测试不同的比较方式
    test_cases = [
        target_order,
        int(target_order),
        str(target_order),
        f"{target_order}",
    ]
    
    print(f"目标订单号: {target_order} (类型: {type(target_order)})")
    print("测试不同类型的比较:")
    
    for i, test_value in enumerate(test_cases, 1):
        print(f"  测试 {i}: {test_value} (类型: {type(test_value)})")
        print(f"    == 比较: {test_value == target_order}")
        print(f"    str() 比较: {str(test_value) == str(target_order)}")

if __name__ == "__main__":
    print("开始调试self.current_orders问题...")
    
    # 调试current_orders问题
    success = debug_current_orders_issue()
    
    # 测试订单号比较
    test_order_comparison()
    
    print(f"\n{'='*60}")
    print("调试结果总结:")
    print(f"订单查找逻辑: {'✅ 正常' if success else '❌ 有问题'}")
    
    if not success:
        print("\n⚠️ 可能的问题:")
        print("1. 订单号数据类型不匹配")
        print("2. 订单列表更新有延迟")
        print("3. 订单过滤逻辑有问题")
        print("4. 缓存和实际数据不同步")
    else:
        print("\n🤔 奇怪的问题:")
        print("调试显示一切正常，但实际充值时却找不到订单")
        print("可能是时序问题或者界面更新延迟导致的")
