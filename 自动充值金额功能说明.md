# 自动充值金额计算功能说明

## 🎯 功能概述

新增了自动充值金额计算功能，实现了以下需求：

1. **自动计算充值金额** = 实付金额 + 设定的加价金额
2. **订单列表的充值金额列保持为空显示**
3. **灵活的充值金额选择**：
   - 如果不设置金额 → 按自动计算的金额充值
   - 如果手动设置了金额 → 按设置的金额充值

## ⚙️ 配置说明

### 加价金额设置
- **当前加价金额**：`1.08元`（从配置文件读取）
- **修改方式**：
  1. 点击界面上的 `⚙️ 配置` 按钮
  2. 在"充值配置"区域修改"加价金额"
  3. 点击"更新加价金额"按钮
  4. 立即生效，无需重启

### 配置文件位置
- 文件名：`manual_recharge_config.ini`
- 配置项：`[Settings]` 段的 `extra_charge`

## 🔧 使用方法

### 1. 订单列表显示
```
┌─────────────┬──────────┬──────────┬────────┬──────────┬──────────────┐
│ 订单号      │ 买家昵称 │ 实付金额 │ 用户ID │ 充值金额 │ 操作         │
├─────────────┼──────────┼──────────┼────────┼──────────┼──────────────┤
│ 123456789   │ 小明     │ 4.50     │ 1001   │ (空)     │ 🟢充值|🔴强制 │
└─────────────┴──────────┴──────────┴────────┴──────────┴──────────────┘
```

**说明**：
- 充值金额列始终显示为空
- 可以点击充值金额列进行手动设置
- 用户ID必须填写

### 2. 自动计算模式（推荐）
**操作步骤**：
1. 填写用户ID：`1001`
2. 充值金额列保持为空
3. 点击充值按钮

**计算逻辑**：
```
充值金额 = 实付金额 + 加价金额
充值金额 = 4.50 + 1.08 = 5.58元
```

**日志显示**：
```
[充值流程] 自动计算充值金额: 实付4.50元 + 加价1.08元 = 5.58元
```

### 3. 手动设置模式
**操作步骤**：
1. 填写用户ID：`1001`
2. 点击充值金额列，输入自定义金额：`6.00`
3. 点击充值按钮

**使用逻辑**：
```
充值金额 = 手动设置的金额
充值金额 = 6.00元（忽略自动计算）
```

**日志显示**：
```
[充值流程] 使用手动设置的充值金额: 6.00元
```

### 4. 清空手动设置
**操作步骤**：
1. 点击充值金额列
2. 清空内容（删除所有文字）
3. 按回车确认

**效果**：
- 充值金额列重新显示为空
- 下次充值时会使用自动计算的金额

**日志显示**：
```
用户清空充值金额，将使用自动计算金额
```

## 📊 充值记录增强

### 失败原因记录
现在充值记录表格新增了"失败原因"列，详细记录充值失败的具体原因：

| 状态 | 失败原因示例 |
|------|-------------|
| 失败 | 获取访问令牌失败 |
| 失败 | 充值API失败: 用户不存在 |
| 拒绝 | 用户余额不为0（当前余额: 0.06元），判定为老用户 |
| 异常 | 系统异常: 网络连接超时 |

### 文件记录格式
充值记录文件 `recharge_records.txt` 的格式也已更新：
```
2025-08-14 18:30:15 | 订单号:123456789 | 买家昵称:小明 | 用户ID:1001 | 充值金额:5.58元 | 类型:普通充值 | 状态:成功
2025-08-14 18:31:20 | 订单号:987654321 | 买家昵称:小红 | 用户ID:1002 | 充值金额:4.50元 | 类型:强制充值 | 状态:失败 | 失败原因:用户不存在
```

## 🎯 使用建议

### 推荐工作流程
1. **日常充值**：
   - 只填写用户ID
   - 充值金额保持为空
   - 让系统自动计算充值金额

2. **特殊情况**：
   - 需要特殊金额时，手动设置充值金额
   - 例如：优惠活动、补偿充值等

3. **批量操作**：
   - 可以先填写多个订单的用户ID
   - 然后逐个点击充值，系统会自动计算每个订单的充值金额

### 注意事项
1. **用户ID必填**：无论哪种模式，用户ID都必须填写
2. **金额验证**：手动设置的充值金额必须是有效数字
3. **加价金额**：可以随时在配置中调整，立即生效
4. **日志记录**：所有操作都有详细的日志记录，便于追踪

## 🔍 故障排查

### 常见问题
1. **充值金额计算错误**：
   - 检查配置文件中的 `extra_charge` 设置
   - 确认实付金额格式正确

2. **无法手动设置金额**：
   - 确保输入的是有效数字
   - 检查是否有特殊字符

3. **充值失败**：
   - 查看"失败原因"列的具体信息
   - 检查日志文件 `manual_recharge.log`

### 日志关键词
- `自动计算充值金额`：自动计算模式
- `使用手动设置的充值金额`：手动设置模式
- `用户清空充值金额`：清空手动设置

## 📈 功能优势

1. **提高效率**：无需手动计算每个订单的充值金额
2. **减少错误**：自动计算避免人工计算错误
3. **灵活性**：支持特殊情况下的手动设置
4. **透明度**：详细的日志记录和失败原因
5. **易于维护**：加价金额可以随时调整

现在您可以享受更加智能和高效的充值体验！🚀
