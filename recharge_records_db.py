#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import logging
import configparser
from datetime import datetime

# 读取配置文件
config = configparser.ConfigParser()
config.read('manual_recharge_config.ini', encoding='utf-8')

# 数据库配置
DB_HOST = config['Database']['host']
DB_PORT = config['Database']['port']
DB_USERNAME = config['Database']['username']
DB_PASSWORD = config['Database']['password']
DB_NAME = config['Database']['database']

class RechargeRecordsDB:
    def __init__(self):
        self.connection = None
        self.connect()
        # 暂时跳过表创建和迁移，避免锁冲突
        # try:
        #     self.create_tables()
        # except Exception as e:
        #     logging.error(f"创建表失败，将跳过迁移并继续运行（降级仅查询已有字段）: {e}")
        logging.info("跳过表创建和迁移，直接使用现有表结构")

    def connect(self):
        """连接到PostgreSQL数据库"""
        try:
            self.connection = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USERNAME,
                password=DB_PASSWORD,
                database=DB_NAME,
                connect_timeout=5
            )
            # 开启自动提交，避免DDL锁
            try:
                self.connection.set_session(autocommit=True)
            except Exception:
                self.connection.autocommit = True

            # 设置查询超时，防止长时间阻塞（单位毫秒）
            try:
                with self.connection.cursor() as _cur:
                    _cur.execute("SET statement_timeout TO 5000")  # 5秒
            except Exception as e:
                logging.warning(f"设置statement_timeout失败: {e}")

            logging.info(f"充值记录数据库连接成功 {DB_HOST}:{DB_PORT}/{DB_NAME}")
        except psycopg2.Error as e:
            logging.error(f"充值记录数据库连接失败: {e}")
            # 如果数据库不存在，尝试创建
            if "does not exist" in str(e):
                self.create_database()
            else:
                raise e

    def create_database(self):
        """创建数据库（如果不存在）"""
        try:
            # 连接到默认的postgres数据库来创建新数据库
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USERNAME,
                password=DB_PASSWORD,
                database='postgres'
            )
            conn.autocommit = True
            cursor = conn.cursor()

            # 创建数据库
            cursor.execute(f"CREATE DATABASE {DB_NAME}")
            logging.info(f"数据库 {DB_NAME} 创建成功")

            cursor.close()
            conn.close()

            # 重新连接到新创建的数据库
            self.connect()

        except psycopg2.Error as e:
            logging.error(f"创建数据库失败: {e}")
            raise e

    def create_tables(self):
        """创建充值记录表"""
        try:
            cursor = self.connection.cursor()

            # 创建充值记录表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS recharge_records (
                id SERIAL PRIMARY KEY,
                recharge_time TIMESTAMP NOT NULL,
                order_no VARCHAR(50) NOT NULL,
                buyer_nick VARCHAR(100),
                user_id VARCHAR(20) NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                recharge_type VARCHAR(20) NOT NULL,
                status VARCHAR(20) NOT NULL,
                failure_reason TEXT,
                is_deducted BOOLEAN DEFAULT FALSE,
                deducted_time TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """

            cursor.execute(create_table_sql)

            # 迁移：为旧表添加新列（若不存在） — 使用更短的语句并单独超时控制
            try:
                cursor.execute("ALTER TABLE recharge_records ADD COLUMN IF NOT EXISTS is_deducted BOOLEAN DEFAULT FALSE;")
            except Exception as e:
                logging.warning(f"执行 is_deducted 迁移失败或已存在: {e}")
            try:
                cursor.execute("ALTER TABLE recharge_records ADD COLUMN IF NOT EXISTS deducted_time TIMESTAMP;")
            except Exception as e:
                logging.warning(f"执行 deducted_time 迁移失败或已存在: {e}")

            # 降低锁冲突：确认默认 autocommit 下 DDL 不被长事务阻塞
            try:
                cursor.execute("SET lock_timeout TO '2s';")
            except Exception as e:
                logging.warning(f"设置 lock_timeout 失败: {e}")

            # 创建索引以提高查询性能
            index_sqls = [
                "CREATE INDEX IF NOT EXISTS idx_recharge_records_order_no ON recharge_records(order_no);",
                "CREATE INDEX IF NOT EXISTS idx_recharge_records_user_id ON recharge_records(user_id);",
                "CREATE INDEX IF NOT EXISTS idx_recharge_records_time ON recharge_records(recharge_time);",
                "CREATE INDEX IF NOT EXISTS idx_recharge_records_status ON recharge_records(status);"
            ]

            for sql in index_sqls:
                cursor.execute(sql)

            self.connection.commit()
            logging.info("充值记录表创建成功")

        except psycopg2.Error as e:
            logging.error(f"创建充值记录表失败: {e}")
            self.connection.rollback()
            raise e
        finally:
            if cursor:
                cursor.close()

    def add_record(self, recharge_time, order_no, buyer_nick, user_id, amount, recharge_type, status, failure_reason=None):
        """
        添加充值记录

        参数:
        - recharge_time: 充值时间 (datetime 或 字符串)
        - order_no: 订单号
        - buyer_nick: 买家昵称
        - user_id: 用户ID
        - amount: 充值金额
        - recharge_type: 充值类型 (普通充值/强制充值)
        - status: 状态 (成功/失败/异常/拒绝)
        - failure_reason: 失败原因 (可选)

        返回:
        - 记录ID (成功) 或 None (失败)
        """
        try:
            cursor = self.connection.cursor()

            # 处理时间格式
            if isinstance(recharge_time, str):
                recharge_time = datetime.strptime(recharge_time, '%Y-%m-%d %H:%M:%S')

            insert_sql = """
            INSERT INTO recharge_records
            (recharge_time, order_no, buyer_nick, user_id, amount, recharge_type, status, failure_reason)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id;
            """

            cursor.execute(insert_sql, (
                recharge_time, order_no, buyer_nick, user_id,
                amount, recharge_type, status, failure_reason
            ))

            record_id = cursor.fetchone()[0]
            self.connection.commit()

            logging.info(f"成功添加充值记录: order_no={order_no}, user_id={user_id}, amount={amount}, status={status}, id={record_id}")
            return record_id

        except psycopg2.Error as e:
            logging.error(f"添加充值记录失败: {e}")
            self.connection.rollback()
            return None
        finally:
            if cursor:
                cursor.close()

    def get_all_records(self, limit=1000):
        """
        获取所有充值记录

        参数:
        - limit: 限制返回记录数量

        返回:
        - 记录列表
        """
        try:
            cursor = self.connection.cursor()

            select_sql = """
            SELECT id, recharge_time, order_no, buyer_nick, user_id,
                   amount, recharge_type, status, failure_reason, is_deducted, deducted_time, created_at
            FROM recharge_records
            ORDER BY recharge_time DESC
            LIMIT %s;
            """

            cursor.execute(select_sql, (limit,))
            records = cursor.fetchall()

            # 如果包含新列查询失败，则回退到老列查询（兼容老库）
            try:
                cursor.execute(select_sql, (limit,))
                records = cursor.fetchall()

                # 新字段查询成功的正常映射
                result = []
                for record in records:
                    result.append({
                        'id': record[0],
                        'recharge_time': record[1],
                        'order_no': record[2],
                        'buyer_nick': record[3],
                        'user_id': record[4],
                        'amount': float(record[5]),
                        'recharge_type': record[6],
                        'status': record[7],
                        'failure_reason': record[8] or '',
                        'is_deducted': bool(record[9]),
                        'deducted_time': record[10],
                        'created_at': record[11]
                    })

                logging.info(f"成功获取 {len(result)} 条充值记录")
                return result

            except Exception as e:
                logging.warning(f"新字段查询失败，使用老字段查询: {e}")
                select_sql_old = """
                SELECT id, recharge_time, order_no, buyer_nick, user_id,
                       amount, recharge_type, status, failure_reason, created_at
                FROM recharge_records
                ORDER BY recharge_time DESC
                LIMIT %s;
                """
                cursor.execute(select_sql_old, (limit,))
                records = cursor.fetchall()

                # 老字段查询的映射（兼容模式）
                result = []
                for record in records:
                    result.append({
                        'id': record[0],
                        'recharge_time': record[1],
                        'order_no': record[2],
                        'buyer_nick': record[3],
                        'user_id': record[4],
                        'amount': float(record[5]),
                        'recharge_type': record[6],
                        'status': record[7],
                        'failure_reason': record[8] or '',
                        'is_deducted': False,  # 老库默认值
                        'deducted_time': None,  # 老库默认值
                        'created_at': record[9]
                    })
                logging.info(f"兼容回退查询返回 {len(result)} 条记录")
                return result

        except psycopg2.Error as e:
            logging.error(f"获取充值记录失败: {e}")
            return []
        finally:
            if cursor:
                cursor.close()

    def get_record_by_order(self, order_no):
        """根据订单号获取充值记录"""
        try:
            cursor = self.connection.cursor()

            select_sql = """
            SELECT id, recharge_time, order_no, buyer_nick, user_id,
                   amount, recharge_type, status, failure_reason, is_deducted, deducted_time, created_at
            FROM recharge_records
            WHERE order_no = %s
            ORDER BY recharge_time DESC;
            """

            cursor.execute(select_sql, (order_no,))
            records = cursor.fetchall()

            result = []
            for record in records:
                result.append({
                    'id': record[0],
                    'recharge_time': record[1],
                    'order_no': record[2],
                    'buyer_nick': record[3],
                    'user_id': record[4],
                    'amount': float(record[5]),
                    'recharge_type': record[6],
                    'status': record[7],
                    'failure_reason': record[8] or '',
                    'is_deducted': bool(record[9]),
                    'deducted_time': record[10],
                    'created_at': record[11]
                })

            return result

        except psycopg2.Error as e:
            logging.error(f"根据订单号获取充值记录失败: {e}")
            return []
        finally:
            if cursor:
                cursor.close()

    def delete_record(self, record_id):
        """删除充值记录（用于扣除功能）"""
        try:
            cursor = self.connection.cursor()

            delete_sql = "DELETE FROM recharge_records WHERE id = %s RETURNING order_no, user_id, amount;"
            cursor.execute(delete_sql, (record_id,))

            deleted_record = cursor.fetchone()
            if deleted_record:
                self.connection.commit()
                logging.info(f"成功删除充值记录: id={record_id}, order_no={deleted_record[0]}")
                return deleted_record
            else:
                logging.warning(f"未找到要删除的充值记录: id={record_id}")
                return None

        except psycopg2.Error as e:
            logging.error(f"删除充值记录失败: {e}")
            self.connection.rollback()
            return None
        finally:
            if cursor:
                cursor.close()

    def mark_deducted(self, record_id):
        """将记录标记为已扣除"""
        try:
            cursor = self.connection.cursor()
            update_sql = """
            UPDATE recharge_records
            SET is_deducted = TRUE,
                deducted_time = CURRENT_TIMESTAMP,
                status = CASE WHEN status = '成功' THEN '已扣除' ELSE status END,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
            RETURNING id;
            """
            cursor.execute(update_sql, (record_id,))
            updated = cursor.fetchone()
            if updated:
                self.connection.commit()
                logging.info(f"记录已标记为已扣除: id={record_id}")
                return True
            else:
                self.connection.rollback()
                logging.warning(f"未找到要标记的记录: id={record_id}")
                return False
        except psycopg2.Error as e:
            logging.error(f"标记记录已扣除失败: {e}")
            self.connection.rollback()
            return False
        finally:
            if cursor:
                cursor.close()

    def get_statistics(self):
        """获取充值记录统计信息"""
        try:
            cursor = self.connection.cursor()

            # 总记录数
            cursor.execute("SELECT COUNT(*) FROM recharge_records;")
            total = cursor.fetchone()[0]

            # 今日记录数
            cursor.execute("""
                SELECT COUNT(*) FROM recharge_records
                WHERE DATE(recharge_time) = CURRENT_DATE;
            """)
            today = cursor.fetchone()[0]

            # 成功记录数
            cursor.execute("SELECT COUNT(*) FROM recharge_records WHERE status = '成功';")
            success = cursor.fetchone()[0]

            # 失败记录数
            cursor.execute("SELECT COUNT(*) FROM recharge_records WHERE status IN ('失败', '异常', '拒绝');")
            failed = cursor.fetchone()[0]

            return {
                'total': total,
                'today': today,
                'success': success,
                'failed': failed
            }

        except psycopg2.Error as e:
            logging.error(f"获取充值记录统计失败: {e}")
            return {'total': 0, 'today': 0, 'success': 0, 'failed': 0}
        finally:
            if cursor:
                cursor.close()

    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logging.info("充值记录数据库连接已关闭")

# 全局数据库实例
_recharge_db = None

def get_recharge_db():
    """获取充值记录数据库实例"""
    global _recharge_db
    if _recharge_db is None:
        _recharge_db = RechargeRecordsDB()
    return _recharge_db

# 便捷函数
def add_recharge_record(recharge_time, order_no, buyer_nick, user_id, amount, recharge_type, status, failure_reason=None):
    """添加充值记录的便捷函数"""
    db = get_recharge_db()
    return db.add_record(recharge_time, order_no, buyer_nick, user_id, amount, recharge_type, status, failure_reason)

def get_all_recharge_records(limit=1000):
    """获取所有充值记录的便捷函数"""
    db = get_recharge_db()
    return db.get_all_records(limit)

def get_recharge_records_by_order(order_no):
    """根据订单号获取充值记录的便捷函数"""
    db = get_recharge_db()
    return db.get_record_by_order(order_no)

def delete_recharge_record(record_id):
    """删除充值记录的便捷函数"""
    db = get_recharge_db()
    return db.delete_record(record_id)

def get_recharge_statistics():
    """获取充值记录统计的便捷函数"""
    db = get_recharge_db()
    return db.get_statistics()
