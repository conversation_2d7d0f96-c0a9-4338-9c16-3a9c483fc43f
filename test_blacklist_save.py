#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from blacklist_db import add_buyer_to_blacklist, check_buyer_in_blacklist, get_blacklist_statistics

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def test_add_buyer_to_blacklist():
    """测试添加buyer_eid到黑名单功能"""
    print("=== 测试添加buyer_eid到黑名单功能 ===")
    
    # 使用真实的订单数据进行测试
    test_buyer_eid = "IGuAyTildHgE8ZX8PS8WCw=="  # 订单4683814922930041727的buyer_eid
    test_order_no = "4683814922930041727"
    test_buyer_nick = "花生喂龙"
    test_user_id = "12345"
    test_recharge_amount = 4.50
    test_notes = "测试充值成功，金额: 4.50元"
    
    try:
        print(f"测试数据:")
        print(f"  buyer_eid: {test_buyer_eid}")
        print(f"  订单号: {test_order_no}")
        print(f"  买家昵称: {test_buyer_nick}")
        print(f"  用户ID: {test_user_id}")
        print(f"  充值金额: {test_recharge_amount}元")
        print(f"  备注: {test_notes}")
        print()
        
        # 1. 先检查是否已存在
        print("步骤1: 检查buyer_eid是否已在黑名单中...")
        is_existing = check_buyer_in_blacklist(test_buyer_eid)
        print(f"结果: {'已存在' if is_existing else '不存在'}")
        
        # 2. 添加到黑名单
        print("\n步骤2: 添加buyer_eid到黑名单...")
        record_id = add_buyer_to_blacklist(
            buyer_eid=test_buyer_eid,
            order_no=test_order_no,
            buyer_nick=test_buyer_nick,
            user_id=str(test_user_id),
            recharge_amount=test_recharge_amount,
            notes=test_notes
        )
        
        if record_id:
            print(f"✅ 成功添加到黑名单，记录ID: {record_id}")
        else:
            print("❌ 添加失败，未返回记录ID")
            return False
        
        # 3. 验证是否添加成功
        print("\n步骤3: 验证是否添加成功...")
        is_added = check_buyer_in_blacklist(test_buyer_eid)
        print(f"结果: {'添加成功' if is_added else '添加失败'}")
        
        # 4. 获取统计信息
        print("\n步骤4: 获取黑名单统计信息...")
        stats = get_blacklist_statistics()
        print(f"总记录数: {stats['total']}")
        print(f"今日新增: {stats['today']}")
        
        return is_added
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        logging.error(f"测试添加黑名单异常: {e}", exc_info=True)
        return False

def test_duplicate_add():
    """测试重复添加相同buyer_eid"""
    print("\n=== 测试重复添加相同buyer_eid ===")
    
    test_buyer_eid = "IGuAyTildHgE8ZX8PS8WCw=="
    test_order_no = "4683814922930041727_DUPLICATE"
    test_buyer_nick = "花生喂龙_更新"
    test_user_id = "54321"
    test_recharge_amount = 5.00
    test_notes = "重复测试充值，金额: 5.00元"
    
    try:
        print("尝试重复添加相同的buyer_eid...")
        record_id = add_buyer_to_blacklist(
            buyer_eid=test_buyer_eid,
            order_no=test_order_no,
            buyer_nick=test_buyer_nick,
            user_id=str(test_user_id),
            recharge_amount=test_recharge_amount,
            notes=test_notes
        )
        
        if record_id:
            print(f"✅ 成功更新记录，记录ID: {record_id}")
            return True
        else:
            print("❌ 更新失败")
            return False
            
    except Exception as e:
        print(f"❌ 重复添加测试异常: {e}")
        logging.error(f"重复添加测试异常: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    print("开始测试黑名单保存功能...")
    
    # 测试基本添加功能
    success1 = test_add_buyer_to_blacklist()
    
    # 测试重复添加功能
    success2 = test_duplicate_add()
    
    print(f"\n{'='*50}")
    print("测试结果总结:")
    print(f"基本添加功能: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"重复添加功能: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！黑名单保存功能正常工作。")
        print("如果充值时没有保存，可能是充值流程中的其他问题。")
    else:
        print("\n⚠️ 测试失败，黑名单保存功能存在问题。")
        print("请检查数据库连接和blacklist_db.py中的代码。")
