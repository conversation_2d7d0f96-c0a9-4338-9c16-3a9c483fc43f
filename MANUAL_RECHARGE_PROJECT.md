# 手动充值独立项目

## 项目概述

本项目是从原始的 `longma.py` 订单充值监控工具中独立出来的手动充值功能模块。保持了所有原有的手动充值功能，同时移除了自动监控相关的代码，使其成为一个专门的手动充值工具。

## 项目文件结构

```
手动充值项目/
├── manual_recharge.py              # 主程序文件
├── manual_recharge_config.ini      # 配置文件
├── requirements.txt                # Python依赖
├── README.md                       # 使用说明
├── test_manual_recharge.py         # 功能测试脚本
├── build_manual_recharge.spec      # PyInstaller打包配置
└── MANUAL_RECHARGE_PROJECT.md      # 项目说明（本文件）
```

## 功能对比

### 保留的功能 ✅
- ✅ 手动充值GUI界面（重新设计为表格布局）
- ✅ 订单列表自动拉取和显示
- ✅ 用户ID输入和自动查询用户类型
- ✅ 自定义充值金额或自动计算
- ✅ 老用户识别和提醒消息
- ✅ 充值操作执行
- ✅ 消息发送功能
- ✅ 充值记录保存
- ✅ 实时日志显示
- ✅ 配置参数在线调整
- ✅ Token管理和自动刷新
- ✅ 错误处理和重试机制

### 移除的功能 ❌
- ❌ 自动监控新用户和订单
- ❌ Redis黑名单功能
- ❌ 定时自动关闭功能
- ❌ 自动充值流水线
- ❌ 批量处理功能

### 新增的功能 🆕
- 🆕 独立的配置文件 `manual_recharge_config.ini`
- 🆕 功能完整性测试脚本
- 🆕 专门的打包配置
- 🆕 详细的使用文档
- 🆕 表格式订单列表展示
- 🆕 自动拉取订单功能
- 🆕 通过手机号自动匹配用户ID
- 🆕 双击选择订单操作

## 核心代码模块

### 1. 工具函数模块
- `md5()` - MD5哈希计算
- `gen_sign()` - API签名生成
- `stable_request()` - 带重试的HTTP请求
- `save_to_file()` - 充值记录保存

### 2. 认证模块
- `get_access_token()` - 获取访问令牌
- `handle_token_error()` - 令牌错误处理
- `refresh_token_if_needed()` - 令牌刷新

### 3. 业务逻辑模块
- `get_user_info()` - 用户信息查询
- `determine_user_type()` - 用户类型判断
- `get_order_by_order_no()` - 订单信息查询
- `manual_recharge_user()` - 用户充值操作
- `send_message()` - 消息发送

### 4. GUI模块
- `ManualRechargeApp` - 主应用类
- `ManualRechargeQueueHandler` - 日志处理器
- 各种事件处理方法

## 配置文件说明

### 原始配置 vs 独立配置

| 配置项 | 原始项目 | 独立项目 | 说明 |
|--------|----------|----------|------|
| 配置文件名 | `config.ini` | `manual_recharge_config.ini` | 避免冲突 |
| Redis配置 | ✅ 包含 | ❌ 移除 | 不需要Redis |
| 自动关闭配置 | ✅ 包含 | ❌ 移除 | 不需要定时关闭 |
| 其他配置 | ✅ 保持 | ✅ 保持 | 完全兼容 |

## 依赖关系

### Python标准库
- `tkinter` - GUI界面
- `threading` - 多线程处理
- `logging` - 日志记录
- `configparser` - 配置文件解析
- `json`, `time`, `hashlib` - 基础工具

### 第三方库
- `requests` - HTTP请求

### 移除的依赖
- `redis` - 原项目使用，独立项目不需要

## 部署和使用

### 开发环境运行
```bash
# 安装依赖
pip install -r requirements.txt

# 配置参数
# 编辑 manual_recharge_config.ini

# 运行程序
python manual_recharge.py
```

### 生产环境打包
```bash
# 安装PyInstaller
pip install pyinstaller

# 打包为可执行文件
pyinstaller build_manual_recharge.spec

# 生成的可执行文件在 dist/ 目录
```

## 测试验证

### 功能测试
```bash
python test_manual_recharge.py
```

### 测试覆盖
- ✅ 模块导入测试
- ✅ 配置文件读取测试
- ✅ 核心函数测试
- ✅ GUI创建测试

## 与原项目的兼容性

### 配置兼容性
- 配置文件格式完全兼容
- 只需要复制相关配置节到新配置文件

### 功能兼容性
- 手动充值逻辑100%保持一致
- API调用方式完全相同
- 用户体验无差异

### 数据兼容性
- 充值记录格式兼容
- 日志格式兼容
- 消息模板兼容

## 维护和扩展

### 代码结构
- 模块化设计，便于维护
- 清晰的功能分离
- 完整的错误处理

### 扩展性
- 易于添加新的充值方式
- 支持新的消息渠道
- 可扩展的配置系统

### 安全性
- 敏感信息配置化
- 完整的输入验证
- 安全的错误处理

## 项目优势

1. **专注性** - 专门用于手动充值，功能聚焦
2. **独立性** - 不依赖Redis等外部服务
3. **轻量化** - 移除不必要的功能和依赖
4. **易部署** - 简化的配置和依赖
5. **易维护** - 清晰的代码结构
6. **兼容性** - 与原项目完全兼容

## 后续计划

- [ ] 添加更多的测试用例
- [ ] 支持批量充值功能
- [ ] 添加充值历史查询
- [ ] 支持更多的消息渠道
- [ ] 添加数据导出功能
