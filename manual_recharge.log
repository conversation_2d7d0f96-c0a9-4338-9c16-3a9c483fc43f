2025-08-14 21:33:07,045 - INFO - 开始从数据库加载充值记录...
2025-08-14 21:33:07,212 - INFO - 充值记录数据库连接成功 **************:5432/blacklist_db
2025-08-14 21:33:09,059 - DEBUG - Starting new HTTPS connection (1): qq.py258.com:443
2025-08-14 21:33:09,192 - DEBUG - https://qq.py258.com:443 "POST /admin-api/system/auth/login HTTP/1.1" 200 None
2025-08-14 21:33:09,194 - INFO - 登录成功，获取到新Token: 36a2364ed0a84e15bb60...
2025-08-14 21:33:09,195 - DEBUG - 生成的签名字符串: 1040442267272517,899a58d3c717faa2ba28d50ec4039356,1755178389,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:33:09,198 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:33:09,541 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178389&sign=ae6ab3b29c64f321ca38e48b487f0c07 HTTP/1.1" 200 None
2025-08-14 21:33:09,546 - INFO - 查询到 50 个订单
2025-08-14 21:33:09,546 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:33:09,546 - INFO - 满足条件的订单: 50 个
2025-08-14 21:33:09,546 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:33:09,546 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:33:16,059 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:33:16,060 - DEBUG - 使用缓存的订单数据
2025-08-14 21:33:26,072 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:33:26,072 - DEBUG - 使用缓存的订单数据
2025-08-14 21:33:36,079 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:33:36,080 - DEBUG - 使用缓存的订单数据
2025-08-14 21:33:46,081 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:33:46,082 - DEBUG - 生成的签名字符串: 1040442267272517,efe5959cd4c432d96ea9735e1df6eb70,1755178426,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:33:46,084 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:33:46,489 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178426&sign=89773cb5331394d82f7f976b782646e7 HTTP/1.1" 200 None
2025-08-14 21:33:46,495 - INFO - 查询到 50 个订单
2025-08-14 21:33:46,495 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:33:46,495 - INFO - 满足条件的订单: 50 个
2025-08-14 21:33:46,496 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:33:46,496 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:33:56,092 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:33:56,093 - DEBUG - 使用缓存的订单数据
2025-08-14 21:34:06,100 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:34:06,101 - DEBUG - 使用缓存的订单数据
2025-08-14 21:34:16,116 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:34:16,116 - DEBUG - 生成的签名字符串: 1040442267272517,ae66af9d15970855524e1d662ab18acb,1755178456,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:34:16,119 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:34:16,511 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178456&sign=a8881659024c4e21ca46450d95e837f2 HTTP/1.1" 200 None
2025-08-14 21:34:16,516 - INFO - 查询到 50 个订单
2025-08-14 21:34:16,517 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:34:16,517 - INFO - 满足条件的订单: 50 个
2025-08-14 21:34:16,517 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:34:16,517 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:34:26,124 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:34:26,125 - DEBUG - 使用缓存的订单数据
2025-08-14 21:34:36,138 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:34:36,139 - DEBUG - 使用缓存的订单数据
2025-08-14 21:34:46,147 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:34:46,147 - DEBUG - 生成的签名字符串: 1040442267272517,4fc5f597f4b267458846efd04fdd7f21,1755178486,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:34:46,150 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:34:46,520 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178486&sign=d30bd59bbf3a9b9514a0e6dc97e3750e HTTP/1.1" 200 None
2025-08-14 21:34:46,526 - INFO - 查询到 50 个订单
2025-08-14 21:34:46,526 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:34:46,526 - INFO - 满足条件的订单: 50 个
2025-08-14 21:34:46,526 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:34:46,527 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:34:56,157 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:34:56,158 - DEBUG - 使用缓存的订单数据
2025-08-14 21:35:06,165 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:35:06,165 - DEBUG - 使用缓存的订单数据
2025-08-14 21:35:16,172 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:35:16,172 - DEBUG - 生成的签名字符串: 1040442267272517,4aef12c2f5e453405fe018035ff4279e,1755178516,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:35:16,175 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:35:16,545 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178516&sign=2145745ab8d4569cc2966aa2b4063431 HTTP/1.1" 200 None
2025-08-14 21:35:16,551 - INFO - 查询到 50 个订单
2025-08-14 21:35:16,551 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:35:16,552 - INFO - 满足条件的订单: 50 个
2025-08-14 21:35:16,552 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:35:16,552 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:35:26,173 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:35:26,173 - DEBUG - 使用缓存的订单数据
2025-08-14 21:35:36,182 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:35:36,182 - DEBUG - 使用缓存的订单数据
2025-08-14 21:35:46,190 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:35:46,190 - DEBUG - 生成的签名字符串: 1040442267272517,b93045a88671aa9b74eeed157df504e2,1755178546,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:35:46,193 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:35:46,652 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178546&sign=88fe9acbe9df6dbdbfc3cb59a026f3e1 HTTP/1.1" 200 None
2025-08-14 21:35:46,658 - INFO - 查询到 50 个订单
2025-08-14 21:35:46,658 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:35:46,658 - INFO - 满足条件的订单: 50 个
2025-08-14 21:35:46,659 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:35:46,659 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:35:56,191 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:35:56,192 - DEBUG - 使用缓存的订单数据
2025-08-14 21:36:06,205 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:36:06,206 - DEBUG - 使用缓存的订单数据
2025-08-14 21:36:16,212 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:36:16,213 - DEBUG - 生成的签名字符串: 1040442267272517,709e5494f85c2dcf2c2ba444ed8f815f,1755178576,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:36:16,219 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:36:16,635 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178576&sign=1ace15066b98660bb3e37895f59cd57c HTTP/1.1" 200 None
2025-08-14 21:36:16,638 - INFO - 查询到 50 个订单
2025-08-14 21:36:16,638 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:36:16,638 - INFO - 满足条件的订单: 50 个
2025-08-14 21:36:16,638 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:36:16,639 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:36:26,228 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:36:26,228 - DEBUG - 使用缓存的订单数据
2025-08-14 21:36:36,231 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:36:36,231 - DEBUG - 使用缓存的订单数据
2025-08-14 21:36:46,241 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:36:46,242 - DEBUG - 生成的签名字符串: 1040442267272517,bae68f048c8c091b4c6c766e68584dd0,1755178606,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:36:46,244 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:36:46,637 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178606&sign=032d81f3994d3b3fba432f5f9270cd11 HTTP/1.1" 200 None
2025-08-14 21:36:46,643 - INFO - 查询到 50 个订单
2025-08-14 21:36:46,644 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:36:46,644 - INFO - 满足条件的订单: 50 个
2025-08-14 21:36:46,644 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:36:46,644 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:36:56,246 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:36:56,247 - DEBUG - 使用缓存的订单数据
2025-08-14 21:37:06,254 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:37:06,254 - DEBUG - 使用缓存的订单数据
2025-08-14 21:37:16,262 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:37:16,262 - DEBUG - 生成的签名字符串: 1040442267272517,6dcdd58a06a859337d7e02f2aa22aec4,1755178636,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:37:16,264 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:37:16,697 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178636&sign=7c3c07865d3564275523359213942223 HTTP/1.1" 200 None
2025-08-14 21:37:16,713 - INFO - 查询到 50 个订单
2025-08-14 21:37:16,713 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:37:16,713 - INFO - 满足条件的订单: 50 个
2025-08-14 21:37:16,713 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:37:16,713 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:37:26,264 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:37:26,265 - DEBUG - 使用缓存的订单数据
2025-08-14 21:37:36,279 - DEBUG - 使用缓存的Token: 36a2364ed0a84e15bb60...
2025-08-14 21:37:36,280 - DEBUG - 使用缓存的订单数据
2025-08-14 21:37:42,296 - INFO - 开始从数据库加载充值记录...
2025-08-14 21:37:42,499 - INFO - 充值记录数据库连接成功 **************:5432/blacklist_db
2025-08-14 21:37:44,302 - DEBUG - Starting new HTTPS connection (1): qq.py258.com:443
2025-08-14 21:37:44,440 - DEBUG - https://qq.py258.com:443 "POST /admin-api/system/auth/login HTTP/1.1" 200 None
2025-08-14 21:37:44,444 - INFO - 登录成功，获取到新Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:37:44,446 - DEBUG - 生成的签名字符串: 1040442267272517,9bd1060be8a8826ced2caea26cd253b7,1755178664,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:37:44,447 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:37:44,872 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178664&sign=8b5b12be262e8dd67ac485b97f27bf3a HTTP/1.1" 200 None
2025-08-14 21:37:44,878 - INFO - 查询到 50 个订单
2025-08-14 21:37:44,879 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:37:44,879 - INFO - 满足条件的订单: 50 个
2025-08-14 21:37:44,879 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:37:44,880 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:37:47,566 - WARNING - 执行表结构迁移失败或已存在: canceling statement due to statement timeout

2025-08-14 21:37:51,296 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:37:51,297 - DEBUG - 使用缓存的订单数据
2025-08-14 21:37:52,596 - WARNING - 执行表结构迁移失败或已存在: canceling statement due to statement timeout

2025-08-14 21:37:57,624 - ERROR - 创建充值记录表失败: canceling statement due to statement timeout

2025-08-14 21:37:57,625 - ERROR - 从数据库加载充值记录失败: canceling statement due to statement timeout

2025-08-14 21:37:57,626 - WARNING - 尝试从文件加载充值记录（降级处理）...
2025-08-14 21:37:57,626 - INFO - 文件加载功能暂未实现，将显示空记录列表
2025-08-14 21:38:01,297 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:38:01,298 - DEBUG - 使用缓存的订单数据
2025-08-14 21:38:11,311 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:38:11,312 - DEBUG - 使用缓存的订单数据
2025-08-14 21:38:21,315 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:38:21,315 - DEBUG - 生成的签名字符串: 1040442267272517,df29e3e2c92aadef5502ebb5ae5a9bcf,1755178701,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:38:21,318 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:38:21,676 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178701&sign=64cb7419cfe8dbee4c2667c100ace8cf HTTP/1.1" 200 None
2025-08-14 21:38:21,681 - INFO - 查询到 50 个订单
2025-08-14 21:38:21,681 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:38:21,682 - INFO - 满足条件的订单: 50 个
2025-08-14 21:38:21,682 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:38:21,682 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:38:31,325 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:38:31,326 - DEBUG - 使用缓存的订单数据
2025-08-14 21:38:41,327 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:38:41,330 - DEBUG - 使用缓存的订单数据
2025-08-14 21:38:51,339 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:38:51,339 - DEBUG - 生成的签名字符串: 1040442267272517,61902ca1a9b9ae5eaded9a883bac3136,1755178731,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:38:51,342 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:38:51,741 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178731&sign=e437198bf1f3891b59c2c82f70d5371f HTTP/1.1" 200 None
2025-08-14 21:38:51,746 - INFO - 查询到 50 个订单
2025-08-14 21:38:51,747 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:38:51,747 - INFO - 满足条件的订单: 50 个
2025-08-14 21:38:51,747 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:38:51,748 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:39:01,353 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:39:01,354 - DEBUG - 使用缓存的订单数据
2025-08-14 21:39:11,362 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:39:11,363 - DEBUG - 使用缓存的订单数据
2025-08-14 21:39:21,375 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:39:21,375 - DEBUG - 生成的签名字符串: 1040442267272517,f049b53e60b322f9a3c3a4baba259d6d,1755178761,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:39:21,379 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:39:21,774 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178761&sign=e3a4aa1b04f67aeabc5f9d5d09ef77ab HTTP/1.1" 200 None
2025-08-14 21:39:21,780 - INFO - 查询到 50 个订单
2025-08-14 21:39:21,780 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:39:21,781 - INFO - 满足条件的订单: 50 个
2025-08-14 21:39:21,781 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:39:21,781 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:39:31,379 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:39:31,379 - DEBUG - 使用缓存的订单数据
2025-08-14 21:39:41,391 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:39:41,391 - DEBUG - 使用缓存的订单数据
2025-08-14 21:39:51,392 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:39:51,392 - DEBUG - 生成的签名字符串: 1040442267272517,0314513e8dd327ea3222a704f543dbd5,1755178791,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:39:51,396 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:39:51,774 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178791&sign=d7cefe3347df08732d56ff485c9dba8f HTTP/1.1" 200 None
2025-08-14 21:39:51,780 - INFO - 查询到 50 个订单
2025-08-14 21:39:51,780 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:39:51,780 - INFO - 满足条件的订单: 50 个
2025-08-14 21:39:51,780 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:39:51,781 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:40:01,401 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:40:01,402 - DEBUG - 使用缓存的订单数据
2025-08-14 21:40:11,412 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:40:11,413 - DEBUG - 使用缓存的订单数据
2025-08-14 21:40:21,420 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:40:21,420 - DEBUG - 生成的签名字符串: 1040442267272517,9f819c9573f5e3ef08fb7f00a8e90be5,1755178821,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:40:21,422 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:40:21,821 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178821&sign=bea8887f486983ff36bdc4120bb14782 HTTP/1.1" 200 None
2025-08-14 21:40:21,824 - INFO - 查询到 50 个订单
2025-08-14 21:40:21,824 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:40:21,824 - INFO - 满足条件的订单: 50 个
2025-08-14 21:40:21,825 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:40:21,825 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:40:31,428 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:40:31,428 - DEBUG - 使用缓存的订单数据
2025-08-14 21:40:41,437 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:40:41,438 - DEBUG - 使用缓存的订单数据
2025-08-14 21:40:51,450 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:40:51,450 - DEBUG - 生成的签名字符串: 1040442267272517,c38880c171503db83016392cda8bc891,1755178851,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:40:51,452 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:40:51,836 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178851&sign=d6d31de4ceac478d25187a0eca66b5a4 HTTP/1.1" 200 None
2025-08-14 21:40:51,839 - INFO - 查询到 50 个订单
2025-08-14 21:40:51,839 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:40:51,840 - INFO - 满足条件的订单: 50 个
2025-08-14 21:40:51,840 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:40:51,840 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:41:01,460 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:41:01,460 - DEBUG - 使用缓存的订单数据
2025-08-14 21:41:11,468 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:41:11,468 - DEBUG - 使用缓存的订单数据
2025-08-14 21:41:21,479 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:41:21,479 - DEBUG - 生成的签名字符串: 1040442267272517,47ea73fe39d8eb609223813981ef9f9f,1755178881,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:41:21,484 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:41:21,906 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178881&sign=a52798a36b0272ccaa172372cca1ca9e HTTP/1.1" 200 None
2025-08-14 21:41:21,912 - INFO - 查询到 50 个订单
2025-08-14 21:41:21,913 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:41:21,914 - INFO - 满足条件的订单: 50 个
2025-08-14 21:41:21,914 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:41:21,915 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:41:31,488 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:41:31,488 - DEBUG - 使用缓存的订单数据
2025-08-14 21:41:41,502 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:41:41,502 - DEBUG - 使用缓存的订单数据
2025-08-14 21:41:51,506 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:41:51,506 - DEBUG - 生成的签名字符串: 1040442267272517,e73c50007bc131b4fc4e38259507932e,1755178911,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:41:51,511 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:41:51,891 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178911&sign=219e2765ffd2c93f9d4ef7262abb3b07 HTTP/1.1" 200 None
2025-08-14 21:41:51,897 - INFO - 查询到 50 个订单
2025-08-14 21:41:51,897 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:41:51,897 - INFO - 满足条件的订单: 50 个
2025-08-14 21:41:51,898 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:41:51,898 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:42:01,516 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:42:01,516 - DEBUG - 使用缓存的订单数据
2025-08-14 21:42:11,518 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:42:11,518 - DEBUG - 使用缓存的订单数据
2025-08-14 21:42:21,519 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:42:21,520 - DEBUG - 生成的签名字符串: 1040442267272517,b4f80f33c5cf19f867ac9dc0531e9450,1755178941,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:42:21,522 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:42:21,893 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178941&sign=1b4b71e32fdff29dfb7a52ba98f32e8a HTTP/1.1" 200 None
2025-08-14 21:42:21,899 - INFO - 查询到 50 个订单
2025-08-14 21:42:21,899 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:42:21,900 - INFO - 满足条件的订单: 50 个
2025-08-14 21:42:21,900 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:42:21,900 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:42:31,521 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:42:31,522 - DEBUG - 使用缓存的订单数据
2025-08-14 21:42:41,523 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:42:41,523 - DEBUG - 使用缓存的订单数据
2025-08-14 21:42:51,528 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:42:51,529 - DEBUG - 生成的签名字符串: 1040442267272517,ef843c26c99c06bf4507de29423cffbe,1755178971,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:42:51,533 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:42:51,948 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755178971&sign=4e24f57542ef01bed9212a1d0c02f079 HTTP/1.1" 200 None
2025-08-14 21:42:51,953 - INFO - 查询到 50 个订单
2025-08-14 21:42:51,953 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:42:51,954 - INFO - 满足条件的订单: 50 个
2025-08-14 21:42:51,954 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:42:51,954 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:43:01,530 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:43:01,530 - DEBUG - 使用缓存的订单数据
2025-08-14 21:43:11,543 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:43:11,544 - DEBUG - 使用缓存的订单数据
2025-08-14 21:43:21,558 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:43:21,559 - DEBUG - 生成的签名字符串: 1040442267272517,3f59f04b04160394a59125127287828a,1755179001,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:43:21,561 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:43:21,933 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179001&sign=25e225d80abfb6c961efe8ce72da0b05 HTTP/1.1" 200 None
2025-08-14 21:43:21,938 - INFO - 查询到 50 个订单
2025-08-14 21:43:21,939 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:43:21,939 - INFO - 满足条件的订单: 50 个
2025-08-14 21:43:21,939 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:43:21,939 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:43:31,568 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:43:31,569 - DEBUG - 使用缓存的订单数据
2025-08-14 21:43:41,582 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:43:41,582 - DEBUG - 使用缓存的订单数据
2025-08-14 21:43:51,589 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:43:51,589 - DEBUG - 生成的签名字符串: 1040442267272517,2edc27218416497600b46262b1fef48c,1755179031,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:43:51,592 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:43:52,011 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179031&sign=0bc2d5627470cf95c4968d3ed04d420e HTTP/1.1" 200 None
2025-08-14 21:43:52,017 - INFO - 查询到 50 个订单
2025-08-14 21:43:52,017 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:43:52,018 - INFO - 满足条件的订单: 50 个
2025-08-14 21:43:52,018 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:43:52,018 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:44:01,593 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:44:01,593 - DEBUG - 使用缓存的订单数据
2025-08-14 21:44:11,608 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:44:11,609 - DEBUG - 使用缓存的订单数据
2025-08-14 21:44:21,614 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:44:21,615 - DEBUG - 生成的签名字符串: 1040442267272517,eed4ebc1278c57bbd98079399be570ad,1755179061,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:44:21,617 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:44:22,009 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179061&sign=799450529ae77a2f3332d640e51560bc HTTP/1.1" 200 None
2025-08-14 21:44:22,014 - INFO - 查询到 50 个订单
2025-08-14 21:44:22,015 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:44:22,015 - INFO - 满足条件的订单: 50 个
2025-08-14 21:44:22,015 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:44:22,015 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:44:31,619 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:44:31,620 - DEBUG - 使用缓存的订单数据
2025-08-14 21:44:41,626 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:44:41,626 - DEBUG - 使用缓存的订单数据
2025-08-14 21:44:51,642 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:44:51,643 - DEBUG - 生成的签名字符串: 1040442267272517,509a3c64dcb2c0dc13913daaa3ac9af7,1755179091,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:44:51,645 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:44:52,091 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179091&sign=c4c6dbed002d11a27c45125da2e58fd9 HTTP/1.1" 200 None
2025-08-14 21:44:52,098 - INFO - 查询到 50 个订单
2025-08-14 21:44:52,098 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:44:52,099 - INFO - 满足条件的订单: 50 个
2025-08-14 21:44:52,099 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:44:52,099 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:45:01,661 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:45:01,662 - DEBUG - 使用缓存的订单数据
2025-08-14 21:45:11,670 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:45:11,671 - DEBUG - 使用缓存的订单数据
2025-08-14 21:45:21,682 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:45:21,682 - DEBUG - 生成的签名字符串: 1040442267272517,a75d62fbb1c8ba3abfb21360b52da0c4,1755179121,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:45:21,686 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:45:22,073 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179121&sign=a629fd73ec9277b9f9e029c005d43f46 HTTP/1.1" 200 None
2025-08-14 21:45:22,078 - INFO - 查询到 50 个订单
2025-08-14 21:45:22,078 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:45:22,079 - INFO - 满足条件的订单: 50 个
2025-08-14 21:45:22,079 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:45:22,079 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:45:31,685 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:45:31,686 - DEBUG - 使用缓存的订单数据
2025-08-14 21:45:41,693 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:45:41,694 - DEBUG - 使用缓存的订单数据
2025-08-14 21:45:51,698 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:45:51,698 - DEBUG - 生成的签名字符串: 1040442267272517,5c4a3cbe61358675bc7eb2f319cf4143,1755179151,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:45:51,701 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:45:52,070 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179151&sign=d0556db9c726ccd96d81b06eed0facb4 HTTP/1.1" 200 None
2025-08-14 21:45:52,075 - INFO - 查询到 50 个订单
2025-08-14 21:45:52,076 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:45:52,076 - INFO - 满足条件的订单: 50 个
2025-08-14 21:45:52,076 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:45:52,076 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:46:01,709 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:46:01,709 - DEBUG - 使用缓存的订单数据
2025-08-14 21:46:11,715 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:46:11,715 - DEBUG - 使用缓存的订单数据
2025-08-14 21:46:21,729 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:46:21,730 - DEBUG - 生成的签名字符串: 1040442267272517,48c4d5f1c35812fa3ca870cd815f12da,1755179181,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:46:21,732 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:46:22,137 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179181&sign=a4decdc0edbd58ad73a8686d1e92120b HTTP/1.1" 200 None
2025-08-14 21:46:22,143 - INFO - 查询到 50 个订单
2025-08-14 21:46:22,144 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:46:22,144 - INFO - 满足条件的订单: 50 个
2025-08-14 21:46:22,144 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:46:22,144 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:46:31,733 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:46:31,733 - DEBUG - 使用缓存的订单数据
2025-08-14 21:46:41,741 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:46:41,742 - DEBUG - 使用缓存的订单数据
2025-08-14 21:46:51,746 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:46:51,747 - DEBUG - 生成的签名字符串: 1040442267272517,474bdd7d6d0b1fe8e79bf94e2bf34140,1755179211,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:46:51,749 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:46:52,166 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179211&sign=bb271cf4c9c02ba7e9b80afa8555a416 HTTP/1.1" 200 None
2025-08-14 21:46:52,169 - INFO - 查询到 50 个订单
2025-08-14 21:46:52,170 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:46:52,170 - INFO - 满足条件的订单: 50 个
2025-08-14 21:46:52,170 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:46:52,170 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:47:01,751 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:47:01,751 - DEBUG - 使用缓存的订单数据
2025-08-14 21:47:11,766 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:47:11,766 - DEBUG - 使用缓存的订单数据
2025-08-14 21:47:21,774 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:47:21,774 - DEBUG - 生成的签名字符串: 1040442267272517,e1b04f2706a436183489c7e988dcdbb1,1755179241,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:47:21,776 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:47:22,182 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179241&sign=aa935619ade33c056b3416b763938f9b HTTP/1.1" 200 None
2025-08-14 21:47:22,187 - INFO - 查询到 50 个订单
2025-08-14 21:47:22,188 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:47:22,188 - INFO - 满足条件的订单: 50 个
2025-08-14 21:47:22,188 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:47:22,188 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:47:31,785 - DEBUG - 使用缓存的Token: 7d8ab75d439d4c0e9fc3...
2025-08-14 21:47:31,785 - DEBUG - 使用缓存的订单数据
2025-08-14 21:47:36,168 - INFO - 开始从数据库加载充值记录...
2025-08-14 21:47:36,361 - INFO - 充值记录数据库连接成功 **************:5432/blacklist_db
2025-08-14 21:47:38,184 - DEBUG - Starting new HTTPS connection (1): qq.py258.com:443
2025-08-14 21:47:38,315 - DEBUG - https://qq.py258.com:443 "POST /admin-api/system/auth/login HTTP/1.1" 200 None
2025-08-14 21:47:38,316 - INFO - 登录成功，获取到新Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:47:38,317 - DEBUG - 生成的签名字符串: 1040442267272517,08591f6bc9846f84c30d68f91784d937,1755179258,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:47:38,321 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:47:38,781 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179258&sign=24e59239fd37d684f08d398170485c92 HTTP/1.1" 200 None
2025-08-14 21:47:38,787 - INFO - 查询到 50 个订单
2025-08-14 21:47:38,787 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:47:38,787 - INFO - 满足条件的订单: 50 个
2025-08-14 21:47:38,787 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:47:38,788 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:47:41,423 - WARNING - 执行 is_deducted 迁移失败或已存在: canceling statement due to statement timeout

2025-08-14 21:47:45,173 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:47:45,174 - DEBUG - 使用缓存的订单数据
2025-08-14 21:47:46,450 - WARNING - 执行 deducted_time 迁移失败或已存在: canceling statement due to statement timeout

2025-08-14 21:47:48,507 - ERROR - 创建充值记录表失败: canceling statement due to lock timeout

2025-08-14 21:47:48,508 - ERROR - 创建表失败，将跳过迁移并继续运行（降级仅查询已有字段）: canceling statement due to lock timeout

2025-08-14 21:47:50,537 - ERROR - 获取充值记录失败: canceling statement due to lock timeout
LINE 4:             FROM recharge_records
                         ^

2025-08-14 21:47:50,537 - INFO - [记录加载] 数据库返回 0 条记录（后台线程）
2025-08-14 21:47:50,539 - INFO - 成功从数据库加载 0 条充值记录
2025-08-14 21:47:55,185 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:47:55,185 - DEBUG - 使用缓存的订单数据
2025-08-14 21:48:05,186 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:48:05,187 - DEBUG - 使用缓存的订单数据
2025-08-14 21:48:15,201 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:48:15,202 - DEBUG - 生成的签名字符串: 1040442267272517,7b5e84c53f8378425de4885edaf627b0,1755179295,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:48:15,204 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:48:15,623 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179295&sign=7e671390118fd8ba4ff59d8d72c669b5 HTTP/1.1" 200 None
2025-08-14 21:48:15,629 - INFO - 查询到 50 个订单
2025-08-14 21:48:15,630 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:48:15,630 - INFO - 满足条件的订单: 50 个
2025-08-14 21:48:15,630 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:48:15,630 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:48:25,212 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:48:25,212 - DEBUG - 使用缓存的订单数据
2025-08-14 21:48:35,220 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:48:35,221 - DEBUG - 使用缓存的订单数据
2025-08-14 21:48:45,233 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:48:45,233 - DEBUG - 生成的签名字符串: 1040442267272517,69c1d9b41706a28191363d90f4199444,1755179325,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:48:45,235 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:48:45,661 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179325&sign=383816e160b49d3db0951cdac36d6c92 HTTP/1.1" 200 None
2025-08-14 21:48:45,665 - INFO - 查询到 50 个订单
2025-08-14 21:48:45,666 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:48:45,667 - INFO - 满足条件的订单: 50 个
2025-08-14 21:48:45,667 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:48:45,667 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:48:55,242 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:48:55,243 - DEBUG - 使用缓存的订单数据
2025-08-14 21:49:05,248 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:49:05,249 - DEBUG - 使用缓存的订单数据
2025-08-14 21:49:15,256 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:49:15,256 - DEBUG - 生成的签名字符串: 1040442267272517,91ab3611aff9a9ebb9b56530cefc1968,1755179355,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:49:15,258 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:49:15,610 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179355&sign=39bd7bf749a707f00b3c9569ae800a68 HTTP/1.1" 200 None
2025-08-14 21:49:15,614 - INFO - 查询到 50 个订单
2025-08-14 21:49:15,614 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:49:15,615 - INFO - 满足条件的订单: 50 个
2025-08-14 21:49:15,615 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:49:15,615 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:49:25,272 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:49:25,273 - DEBUG - 使用缓存的订单数据
2025-08-14 21:49:35,283 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:49:35,283 - DEBUG - 使用缓存的订单数据
2025-08-14 21:49:45,293 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:49:45,293 - DEBUG - 生成的签名字符串: 1040442267272517,923ba590b34cfa025ff2060dca09893a,1755179385,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:49:45,295 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:49:45,710 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179385&sign=60aec77bcdc08f726ed6046bb8b251bb HTTP/1.1" 200 None
2025-08-14 21:49:45,716 - INFO - 查询到 50 个订单
2025-08-14 21:49:45,716 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:49:45,717 - INFO - 满足条件的订单: 50 个
2025-08-14 21:49:45,717 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:49:45,717 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:49:55,296 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:49:55,296 - DEBUG - 使用缓存的订单数据
2025-08-14 21:50:05,298 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:50:05,299 - DEBUG - 使用缓存的订单数据
2025-08-14 21:50:15,312 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:50:15,312 - DEBUG - 生成的签名字符串: 1040442267272517,e7ca32947b0f86ea6c95373bca9eb610,1755179415,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:50:15,315 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:50:15,717 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179415&sign=b82e3e1ff344d96f1237dee4df8235ba HTTP/1.1" 200 None
2025-08-14 21:50:15,722 - INFO - 查询到 50 个订单
2025-08-14 21:50:15,723 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:50:15,723 - INFO - 满足条件的订单: 50 个
2025-08-14 21:50:15,723 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:50:15,723 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:50:25,320 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:50:25,321 - DEBUG - 使用缓存的订单数据
2025-08-14 21:50:35,324 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:50:35,325 - DEBUG - 使用缓存的订单数据
2025-08-14 21:50:45,339 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:50:45,339 - DEBUG - 生成的签名字符串: 1040442267272517,946cd9dce0d7c2affc3e09e70827419c,1755179445,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:50:45,341 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:50:45,728 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179445&sign=17bfa97b06e4e66765a66f254bdd59f1 HTTP/1.1" 200 None
2025-08-14 21:50:45,734 - INFO - 查询到 50 个订单
2025-08-14 21:50:45,734 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:50:45,734 - INFO - 满足条件的订单: 50 个
2025-08-14 21:50:45,735 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:50:45,735 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:50:55,343 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:50:55,344 - DEBUG - 使用缓存的订单数据
2025-08-14 21:51:05,344 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:51:05,344 - DEBUG - 使用缓存的订单数据
2025-08-14 21:51:15,353 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:51:15,354 - DEBUG - 生成的签名字符串: 1040442267272517,32440c099e0a9b6aed993eb1ccfd6554,1755179475,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:51:15,356 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:51:15,781 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179475&sign=5b921d213b5c80f7f8ab47fce922ed55 HTTP/1.1" 200 None
2025-08-14 21:51:15,788 - INFO - 查询到 50 个订单
2025-08-14 21:51:15,788 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:51:15,789 - INFO - 满足条件的订单: 50 个
2025-08-14 21:51:15,789 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:51:15,789 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:51:25,357 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:51:25,358 - DEBUG - 使用缓存的订单数据
2025-08-14 21:51:35,360 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:51:35,360 - DEBUG - 使用缓存的订单数据
2025-08-14 21:51:45,361 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:51:45,361 - DEBUG - 生成的签名字符串: 1040442267272517,ca24a4daac7f90be6b0cfde1a66a46cd,1755179505,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:51:45,363 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:51:45,798 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179505&sign=f7364b59412c537beba9efe06442dbfa HTTP/1.1" 200 None
2025-08-14 21:51:45,803 - INFO - 查询到 50 个订单
2025-08-14 21:51:45,803 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:51:45,804 - INFO - 满足条件的订单: 50 个
2025-08-14 21:51:45,804 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:51:45,804 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:51:55,365 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:51:55,366 - DEBUG - 使用缓存的订单数据
2025-08-14 21:52:05,368 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:52:05,368 - DEBUG - 使用缓存的订单数据
2025-08-14 21:52:15,376 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:52:15,376 - DEBUG - 生成的签名字符串: 1040442267272517,39f1402d2135ff03c2d710c90b15df76,1755179535,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:52:15,378 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:52:15,733 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179535&sign=23a54956ee5ef3768977fd0cb8a35b22 HTTP/1.1" 200 None
2025-08-14 21:52:15,738 - INFO - 查询到 50 个订单
2025-08-14 21:52:15,738 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:52:15,738 - INFO - 满足条件的订单: 50 个
2025-08-14 21:52:15,739 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:52:15,739 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:52:25,380 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:52:25,381 - DEBUG - 使用缓存的订单数据
2025-08-14 21:52:35,389 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:52:35,389 - DEBUG - 使用缓存的订单数据
2025-08-14 21:52:45,392 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:52:45,392 - DEBUG - 生成的签名字符串: 1040442267272517,c101cf63cbbc750b3165c0efdf36049f,1755179565,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:52:45,394 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:52:45,838 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179565&sign=1f599ebc7b6aab3cb3ca77d37a715045 HTTP/1.1" 200 None
2025-08-14 21:52:45,843 - INFO - 查询到 50 个订单
2025-08-14 21:52:45,844 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:52:45,844 - INFO - 满足条件的订单: 50 个
2025-08-14 21:52:45,844 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:52:45,844 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:52:55,406 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:52:55,406 - DEBUG - 使用缓存的订单数据
2025-08-14 21:53:05,414 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:53:05,415 - DEBUG - 使用缓存的订单数据
2025-08-14 21:53:15,423 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:53:15,424 - DEBUG - 生成的签名字符串: 1040442267272517,a373a7d619dcb2fd9dc6fe09874d8f11,1755179595,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:53:15,427 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:53:15,803 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179595&sign=2b0cfbde2d34161e99ecef9a6efbdd4d HTTP/1.1" 200 None
2025-08-14 21:53:15,807 - INFO - 查询到 50 个订单
2025-08-14 21:53:15,808 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:53:15,808 - INFO - 满足条件的订单: 50 个
2025-08-14 21:53:15,809 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:53:15,809 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:53:25,424 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:53:25,424 - DEBUG - 使用缓存的订单数据
2025-08-14 21:53:35,426 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:53:35,426 - DEBUG - 使用缓存的订单数据
2025-08-14 21:53:45,433 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:53:45,434 - DEBUG - 生成的签名字符串: 1040442267272517,f6cdc42c677caf1d52f16324ff7e48bf,1755179625,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:53:45,440 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:53:45,939 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179625&sign=f99c8906a296d36282adddd3b2a4de69 HTTP/1.1" 200 None
2025-08-14 21:53:45,945 - INFO - 查询到 50 个订单
2025-08-14 21:53:45,946 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:53:45,946 - INFO - 满足条件的订单: 50 个
2025-08-14 21:53:45,947 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:53:45,947 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:53:55,439 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:53:55,440 - DEBUG - 使用缓存的订单数据
2025-08-14 21:54:05,441 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:54:05,441 - DEBUG - 使用缓存的订单数据
2025-08-14 21:54:15,454 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:54:15,454 - DEBUG - 生成的签名字符串: 1040442267272517,16deec324c3f58f3b5de3f1ea14ad5eb,1755179655,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:54:15,456 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:54:15,818 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179655&sign=c99d6250dc061ded3c80a8661063940d HTTP/1.1" 200 None
2025-08-14 21:54:15,824 - INFO - 查询到 50 个订单
2025-08-14 21:54:15,824 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:54:15,825 - INFO - 满足条件的订单: 50 个
2025-08-14 21:54:15,825 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:54:15,826 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:54:25,457 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:54:25,458 - DEBUG - 使用缓存的订单数据
2025-08-14 21:54:35,471 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:54:35,472 - DEBUG - 使用缓存的订单数据
2025-08-14 21:54:45,482 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:54:45,482 - DEBUG - 生成的签名字符串: 1040442267272517,f652865951562b2375d3368a7f3925d4,1755179685,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:54:45,485 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:54:45,877 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179685&sign=7155272e9ee95fa18f18fea58d9e2e4b HTTP/1.1" 200 None
2025-08-14 21:54:45,883 - INFO - 查询到 50 个订单
2025-08-14 21:54:45,884 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:54:45,884 - INFO - 满足条件的订单: 50 个
2025-08-14 21:54:45,885 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:54:45,886 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:54:55,481 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:54:55,481 - DEBUG - 使用缓存的订单数据
2025-08-14 21:55:05,490 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:55:05,490 - DEBUG - 使用缓存的订单数据
2025-08-14 21:55:15,492 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:55:15,493 - DEBUG - 生成的签名字符串: 1040442267272517,b1d463d6d61e9b0d1009e301a632929d,1755179715,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:55:15,495 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:55:15,870 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179715&sign=26be53c993a229a1cb3d50c11b1059f3 HTTP/1.1" 200 None
2025-08-14 21:55:15,875 - INFO - 查询到 50 个订单
2025-08-14 21:55:15,875 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:55:15,875 - INFO - 满足条件的订单: 50 个
2025-08-14 21:55:15,876 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:55:15,876 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:55:25,499 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:55:25,500 - DEBUG - 使用缓存的订单数据
2025-08-14 21:55:35,503 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:55:35,504 - DEBUG - 使用缓存的订单数据
2025-08-14 21:55:45,514 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:55:45,515 - DEBUG - 生成的签名字符串: 1040442267272517,469d4006b72f0056e116a8a4191dad83,1755179745,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:55:45,517 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:55:45,937 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179745&sign=6b0daecf4b79e5bfee5162ea20f643b0 HTTP/1.1" 200 None
2025-08-14 21:55:45,942 - INFO - 查询到 50 个订单
2025-08-14 21:55:45,942 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:55:45,942 - INFO - 满足条件的订单: 50 个
2025-08-14 21:55:45,943 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:55:45,943 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:55:55,517 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:55:55,518 - DEBUG - 使用缓存的订单数据
2025-08-14 21:56:05,523 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:56:05,523 - DEBUG - 使用缓存的订单数据
2025-08-14 21:56:15,527 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:56:15,527 - DEBUG - 生成的签名字符串: 1040442267272517,b0247124fa97a044fe18e67e25819d6d,1755179775,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:56:15,532 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:56:15,911 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179775&sign=bd50edd52cf3fe3f9b9f485d3c9d06b9 HTTP/1.1" 200 None
2025-08-14 21:56:15,916 - INFO - 查询到 50 个订单
2025-08-14 21:56:15,917 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:56:15,917 - INFO - 满足条件的订单: 50 个
2025-08-14 21:56:15,917 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:56:15,918 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:56:25,527 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:56:25,528 - DEBUG - 使用缓存的订单数据
2025-08-14 21:56:35,539 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:56:35,540 - DEBUG - 使用缓存的订单数据
2025-08-14 21:56:45,541 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:56:45,541 - DEBUG - 生成的签名字符串: 1040442267272517,1f07c9f04e94cfb61b5334bc39a357d0,1755179805,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:56:45,543 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:56:45,961 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179805&sign=b5c725592136a23ab80c7170c0c8e815 HTTP/1.1" 200 None
2025-08-14 21:56:45,966 - INFO - 查询到 50 个订单
2025-08-14 21:56:45,967 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:56:45,968 - INFO - 满足条件的订单: 50 个
2025-08-14 21:56:45,968 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:56:45,968 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:56:55,542 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:56:55,542 - DEBUG - 使用缓存的订单数据
2025-08-14 21:57:05,545 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:57:05,545 - DEBUG - 使用缓存的订单数据
2025-08-14 21:57:15,558 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:57:15,558 - DEBUG - 生成的签名字符串: 1040442267272517,37bd79c870b32ec937b969320a243af3,1755179835,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:57:15,560 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:57:15,961 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179835&sign=887ec1596b1231d95cca3d1f22d0b6f6 HTTP/1.1" 200 None
2025-08-14 21:57:15,964 - INFO - 查询到 50 个订单
2025-08-14 21:57:15,965 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:57:15,965 - INFO - 满足条件的订单: 50 个
2025-08-14 21:57:15,965 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:57:15,965 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:57:25,601 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:57:25,601 - DEBUG - 使用缓存的订单数据
2025-08-14 21:57:35,610 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:57:35,611 - DEBUG - 使用缓存的订单数据
2025-08-14 21:57:45,618 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:57:45,619 - DEBUG - 生成的签名字符串: 1040442267272517,92141e650305ada1cbbae0fed9923ac2,1755179865,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:57:45,621 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:57:45,993 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179865&sign=837685e22b7d8b42393a1e67bd2aeea2 HTTP/1.1" 200 None
2025-08-14 21:57:46,001 - INFO - 查询到 50 个订单
2025-08-14 21:57:46,002 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:57:46,002 - INFO - 满足条件的订单: 50 个
2025-08-14 21:57:46,002 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:57:46,003 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:57:55,627 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:57:55,628 - DEBUG - 使用缓存的订单数据
2025-08-14 21:58:05,635 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:58:05,636 - DEBUG - 使用缓存的订单数据
2025-08-14 21:58:15,638 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:58:15,638 - DEBUG - 生成的签名字符串: 1040442267272517,f5acbd4d3ed89975b04a8dd6477aa538,1755179895,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:58:15,640 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:58:16,018 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179895&sign=fe371822dffc31a3136eb3540ed04d4b HTTP/1.1" 200 None
2025-08-14 21:58:16,024 - INFO - 查询到 50 个订单
2025-08-14 21:58:16,024 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:58:16,024 - INFO - 满足条件的订单: 50 个
2025-08-14 21:58:16,025 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:58:16,025 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:58:25,649 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:58:25,649 - DEBUG - 使用缓存的订单数据
2025-08-14 21:58:35,663 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:58:35,663 - DEBUG - 使用缓存的订单数据
2025-08-14 21:58:45,675 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:58:45,676 - DEBUG - 生成的签名字符串: 1040442267272517,f73798fd0a709ba300034ab5f9b6e0df,1755179925,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:58:45,678 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:58:46,034 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179925&sign=9fb81d38cc468aeaf58fd6728d3f13d9 HTTP/1.1" 200 None
2025-08-14 21:58:46,039 - INFO - 查询到 50 个订单
2025-08-14 21:58:46,039 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:58:46,040 - INFO - 满足条件的订单: 50 个
2025-08-14 21:58:46,045 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:58:46,046 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:58:55,679 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:58:55,679 - DEBUG - 使用缓存的订单数据
2025-08-14 21:59:05,687 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:59:05,687 - DEBUG - 使用缓存的订单数据
2025-08-14 21:59:15,702 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:59:15,703 - DEBUG - 生成的签名字符串: 1040442267272517,d552d367c70ec33fa847dd4a4ac02fce,1755179955,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 21:59:15,707 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 21:59:16,110 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755179955&sign=a6f2c40fb161216ec79b00ac137fcccd HTTP/1.1" 200 None
2025-08-14 21:59:16,116 - INFO - 查询到 50 个订单
2025-08-14 21:59:16,116 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 21:59:16,116 - INFO - 满足条件的订单: 50 个
2025-08-14 21:59:16,117 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 21:59:16,117 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 21:59:25,711 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:59:25,712 - DEBUG - 使用缓存的订单数据
2025-08-14 21:59:35,723 - DEBUG - 使用缓存的Token: ab2e778c5d1f4f1c9181...
2025-08-14 21:59:35,724 - DEBUG - 使用缓存的订单数据
2025-08-14 22:23:22,647 - INFO - 开始从数据库加载充值记录...
2025-08-14 22:23:22,859 - INFO - 充值记录数据库连接成功 **************:5432/blacklist_db
2025-08-14 22:23:22,859 - INFO - 跳过表创建和迁移，直接使用现有表结构
2025-08-14 22:23:24,655 - DEBUG - Starting new HTTPS connection (1): qq.py258.com:443
2025-08-14 22:23:24,766 - DEBUG - https://qq.py258.com:443 "POST /admin-api/system/auth/login HTTP/1.1" 200 None
2025-08-14 22:23:24,766 - INFO - 登录成功，获取到新Token: e29e259d5bca423ca87a...
2025-08-14 22:23:24,766 - DEBUG - 生成的签名字符串: 1040442267272517,01fe71344445884bcd5b760102e9a5c4,1755181404,dX8nDy00vE0imy5AdTivWhfkY3ZnAZjz
2025-08-14 22:23:24,770 - DEBUG - Starting new HTTPS connection (1): open.goofish.pro:443
2025-08-14 22:23:25,161 - DEBUG - https://open.goofish.pro:443 "POST /api/open/order/list?appid=1040442267272517&timestamp=1755181404&sign=419f81463b66b3950acafba3436579b2 HTTP/1.1" 200 None
2025-08-14 22:23:25,166 - INFO - 查询到 50 个订单
2025-08-14 22:23:25,166 - INFO - 跳过用户信息查询，直接处理订单
2025-08-14 22:23:25,166 - INFO - 满足条件的订单: 50 个
2025-08-14 22:23:25,166 - INFO - 第一个订单数据结构: ['order_no', 'order_status', 'order_type', 'order_time', 'total_amount', 'pay_amount', 'pay_no', 'pay_time', 'refund_status', 'refund_amount', 'refund_time', 'receiver_mobile', 'receiver_name', 'prov_name', 'city_name', 'area_name', 'town_name', 'address', 'waybill_no', 'express_code', 'express_name', 'express_fee', 'consign_type', 'consign_time', 'confirm_time', 'cancel_reason', 'cancel_time', 'create_time', 'update_time', 'buyer_eid', 'buyer_nick', 'seller_eid', 'seller_name', 'seller_remark', 'idle_biz_type', 'pin_group_status', 'goods', 'xyb_seller_amount', 'pay_amount_yuan', 'estimated_recharge']
2025-08-14 22:23:25,166 - INFO - 第一个订单用户信息: user_id=None, user_type=None
2025-08-14 22:23:26,387 - INFO - 用户清空充值金额，将使用自动计算金额
2025-08-14 22:23:27,888 - ERROR - 获取充值记录失败: canceling statement due to statement timeout

2025-08-14 22:23:27,888 - INFO - [记录加载] 数据库返回 0 条记录（后台线程）
2025-08-14 22:23:27,893 - INFO - 成功从数据库加载 0 条充值记录
2025-08-14 22:23:31,646 - INFO - 用户正在编辑数据，跳过本次自动刷新
