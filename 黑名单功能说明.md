# 黑名单功能使用说明

## 功能概述

本次更新为手动充值版本添加了黑名单数据库功能，在充值成功后会自动将买家的 `buyer_eid` 保存到黑名单数据库中，并在后续充值时进行检查。

## 主要功能

### 1. 黑名单数据库
- **数据库类型**: PostgreSQL
- **服务器地址**: **************:5432
- **数据库名**: blacklist_db
- **表名**: blacklist

### 2. 充值前检查（仅普通充值）
普通充值（绿色按钮）会进行以下三项检查：

1. **用户余额检查**: 检查用户余额是否为0
2. **黑名单检查**: 检查买家的 `buyer_eid` 是否已在黑名单中
3. **订单数检查**: 检查用户订单数是否超过设定限制（当前限制：0单）

如果任何一项检查不通过，会显示老用户提示并拒绝充值。

### 3. 强制充值（红色按钮）
强制充值会跳过所有检查，直接执行充值操作。

### 4. 自动保存黑名单
充值成功后，系统会自动：
- 获取订单中的 `buyer_eid`
- 将 `buyer_eid` 及相关信息保存到黑名单数据库
- 记录充值金额、订单号、买家昵称等信息

## 配置文件更新

在 `manual_recharge_config.ini` 中新增了数据库配置：

```ini
[Database]
# PostgreSQL数据库配置
host = **************
port = 5432
username = ysh1998
password = a63006320
database = blacklist_db
```

## 数据库表结构

```sql
CREATE TABLE blacklist (
    id SERIAL PRIMARY KEY,
    buyer_eid VARCHAR(255) UNIQUE NOT NULL,
    order_no VARCHAR(255),
    buyer_nick VARCHAR(255),
    user_id VARCHAR(100),
    recharge_amount DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
);
```

## 使用方法

### 普通充值流程
1. 在订单列表中填写用户ID和充值金额
2. 点击绿色的"🟢充值"按钮
3. 系统自动检查用户资格：
   - 余额是否为0
   - 是否在黑名单中
   - 订单数是否符合要求
4. 如果检查通过，显示确认对话框
5. 确认后执行充值并自动保存到黑名单

### 强制充值流程
1. 在订单列表中填写用户ID和充值金额
2. 点击红色的"🔴强制充值"按钮
3. 直接显示确认对话框（跳过所有检查）
4. 确认后执行充值并自动保存到黑名单

## 日志记录

系统会记录以下关键信息：
- 充值前检查结果
- 黑名单保存操作
- 数据库连接状态
- 错误和异常信息

## 测试文件

- `test_db.py`: 测试数据库连接和基本功能
- `test_recharge_check.py`: 测试充值前检查逻辑
- `blacklist_db.py`: 黑名单数据库操作模块

## 注意事项

1. 确保数据库服务器的5432端口已开放
2. 首次运行时会自动创建数据库和表
3. 黑名单记录支持重复更新（相同buyer_eid会更新现有记录）
4. 强制充值功能请谨慎使用，建议仅在特殊情况下使用

## 故障排除

如果遇到数据库连接问题：
1. 检查网络连接
2. 确认数据库服务器状态
3. 验证配置文件中的数据库信息
4. 查看日志文件中的详细错误信息
