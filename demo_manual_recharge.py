#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动充值工具演示脚本
展示新的表格界面和功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time

class DemoApp:
    def __init__(self, master):
        self.master = master
        self.master.title("手动充值工具 - 演示模式")
        self.master.geometry("1000x700")

        # 演示数据
        self.demo_orders = [
            {
                'order_no': '2024011234567890',
                'pay_amount_yuan': 5.20,
                'receiver_mobile': '13812345678',
                'buyer_nick': '用户A',
                'estimated_recharge': 6.40
            },
            {
                'order_no': '2024011234567891',
                'pay_amount_yuan': 8.50,
                'receiver_mobile': '13987654321',
                'buyer_nick': '用户B',
                'estimated_recharge': 9.70
            },
            {
                'order_no': '2024011234567892',
                'pay_amount_yuan': 12.00,
                'receiver_mobile': '13666666666',
                'buyer_nick': '用户C',
                'estimated_recharge': 13.20
            }
        ]

        self.setup_ui()

    def setup_ui(self):
        # 标题
        title_frame = ttk.Frame(self.master)
        title_frame.pack(fill='x', padx=10, pady=5)
        ttk.Label(title_frame, text="手动充值工具 - 新界面演示", font=('Arial', 14, 'bold')).pack()
        ttk.Label(title_frame, text="这是演示模式，展示新的表格界面和操作流程", foreground='blue').pack()

        # 主框架
        main_frame = ttk.Frame(self.master)
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 控制区
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill='x', pady=(0, 10))

        ttk.Button(control_frame, text="刷新订单列表", command=self.refresh_orders).pack(side='left', padx=5)
        self.status_var = tk.StringVar(value="点击刷新按钮获取订单")
        ttk.Label(control_frame, textvariable=self.status_var, foreground='blue').pack(side='left', padx=10)

        # 内容区
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill='both', expand=True)

        # 左侧：订单列表
        orders_frame = ttk.Labelframe(content_frame, text="订单列表")
        orders_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))

        # 创建表格
        columns = ('order_no', 'pay_amount', 'receiver_mobile', 'buyer_nick', 'estimated_recharge')
        self.orders_tree = ttk.Treeview(orders_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题
        self.orders_tree.heading('order_no', text='订单号')
        self.orders_tree.heading('pay_amount', text='实付金额(元)')
        self.orders_tree.heading('receiver_mobile', text='收货手机号')
        self.orders_tree.heading('buyer_nick', text='买家昵称')
        self.orders_tree.heading('estimated_recharge', text='预计充值(元)')

        # 设置列宽
        self.orders_tree.column('order_no', width=150)
        self.orders_tree.column('pay_amount', width=100)
        self.orders_tree.column('receiver_mobile', width=120)
        self.orders_tree.column('buyer_nick', width=120)
        self.orders_tree.column('estimated_recharge', width=120)

        # 滚动条
        scrollbar = ttk.Scrollbar(orders_frame, orient='vertical', command=self.orders_tree.yview)
        self.orders_tree.configure(yscrollcommand=scrollbar.set)

        self.orders_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y', pady=5)

        # 绑定双击事件
        self.orders_tree.bind('<Double-1>', self.on_order_select)

        # 右侧：操作区
        right_frame = ttk.Frame(content_frame)
        right_frame.pack(side='right', fill='y', padx=(5, 0))

        # 用户信息
        user_frame = ttk.Labelframe(right_frame, text="用户信息")
        user_frame.pack(fill='x', pady=(0, 10))

        ttk.Label(user_frame, text="用户ID:").pack(anchor='w', padx=5)
        self.user_id_var = tk.StringVar()
        ttk.Entry(user_frame, textvariable=self.user_id_var, width=20).pack(fill='x', padx=5, pady=2)

        self.user_info_var = tk.StringVar(value="请选择订单")
        ttk.Label(user_frame, textvariable=self.user_info_var, foreground='blue', wraplength=200).pack(fill='x', padx=5, pady=5)

        # 充值操作
        recharge_frame = ttk.Labelframe(right_frame, text="充值操作")
        recharge_frame.pack(fill='x', pady=(0, 10))

        self.selected_order_var = tk.StringVar(value="请选择订单")
        ttk.Label(recharge_frame, textvariable=self.selected_order_var, foreground='green', wraplength=200).pack(fill='x', padx=5, pady=5)

        ttk.Label(recharge_frame, text="自定义金额:").pack(anchor='w', padx=5)
        self.custom_amount_var = tk.StringVar()
        ttk.Entry(recharge_frame, textvariable=self.custom_amount_var, width=15).pack(fill='x', padx=5, pady=2)

        ttk.Button(recharge_frame, text="执行充值", command=self.demo_recharge).pack(fill='x', padx=5, pady=5)

        # 说明
        info_frame = ttk.Labelframe(right_frame, text="操作说明")
        info_frame.pack(fill='both', expand=True)

        info_text = """新界面特性：

1. 自动拉取订单列表
2. 表格形式展示订单
3. 双击选择订单
4. 自动匹配用户ID
5. 预计算充值金额
6. 简化操作流程

演示操作：
• 点击"刷新订单列表"
• 双击任意订单行
• 查看自动填入的信息
• 点击"执行充值"体验"""

        ttk.Label(info_frame, text=info_text, justify='left', wraplength=180).pack(fill='both', expand=True, padx=5, pady=5)

    def refresh_orders(self):
        """刷新订单列表（演示）"""
        self.status_var.set("正在获取订单...")
        
        # 模拟网络请求延迟
        def load_orders():
            time.sleep(1)
            self.master.after(0, self.update_orders_display)
        
        threading.Thread(target=load_orders, daemon=True).start()

    def update_orders_display(self):
        """更新订单显示"""
        # 清空现有数据
        for item in self.orders_tree.get_children():
            self.orders_tree.delete(item)
        
        # 添加演示数据
        for order in self.demo_orders:
            self.orders_tree.insert('', 'end', values=(
                order['order_no'],
                f"{order['pay_amount_yuan']:.2f}",
                order['receiver_mobile'],
                order['buyer_nick'],
                f"{order['estimated_recharge']:.2f}"
            ))
        
        self.status_var.set(f"已获取 {len(self.demo_orders)} 个订单（演示数据）")

    def on_order_select(self, event=None):
        """选择订单"""
        selection = self.orders_tree.selection()
        if selection:
            item = selection[0]
            values = self.orders_tree.item(item, 'values')
            if values:
                order_no = values[0]
                pay_amount = values[1]
                mobile = values[2]
                estimated_recharge = values[4]
                
                # 更新显示
                self.selected_order_var.set(f"订单: {order_no}\n实付: {pay_amount}元\n预计充值: {estimated_recharge}元")
                
                # 模拟自动查询用户ID
                demo_user_id = mobile[-4:]  # 使用手机号后4位作为演示用户ID
                self.user_id_var.set(demo_user_id)
                self.user_info_var.set(f"新用户 (平台统计:0, 余额:0)\n手机: {mobile}")

    def demo_recharge(self):
        """演示充值操作"""
        if not self.user_id_var.get():
            messagebox.showwarning("提示", "请先选择订单")
            return
        
        # 模拟充值过程
        messagebox.showinfo("演示", "这是演示模式\n\n在实际使用中，这里会：\n1. 验证用户信息\n2. 执行充值操作\n3. 发送通知消息\n4. 记录充值日志")

if __name__ == "__main__":
    root = tk.Tk()
    app = DemoApp(root)
    root.mainloop()
