#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import configparser
from datetime import datetime

# 读取配置文件
config = configparser.ConfigParser()
config.read('manual_recharge_config.ini', encoding='utf-8')

# 数据库配置
DB_HOST = config['Database']['host']
DB_PORT = config['Database']['port']
DB_USERNAME = config['Database']['username']
DB_PASSWORD = config['Database']['password']
DB_NAME = config['Database']['database']

def search_order_in_blacklist(order_no):
    """在黑名单数据库中搜索指定订单号"""
    try:
        # 连接数据库
        connection = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USERNAME,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        
        cursor = connection.cursor()
        
        print(f"=== 搜索订单号: {order_no} ===")
        print(f"数据库: {DB_HOST}:{DB_PORT}/{DB_NAME}")
        print("-" * 60)
        
        # 搜索指定订单号
        query = """
        SELECT id, buyer_eid, order_no, buyer_nick, user_id, recharge_amount, 
               created_at, updated_at, notes
        FROM blacklist 
        WHERE order_no = %s
        ORDER BY created_at DESC
        """
        
        cursor.execute(query, (order_no,))
        records = cursor.fetchall()
        
        if records:
            print(f"✅ 找到 {len(records)} 条匹配记录:")
            print()
            
            for i, record in enumerate(records, 1):
                id_val, buyer_eid, order_no_db, buyer_nick, user_id, recharge_amount, created_at, updated_at, notes = record
                
                print(f"记录 {i}:")
                print(f"  ID: {id_val}")
                print(f"  buyer_eid: {buyer_eid}")
                print(f"  订单号: {order_no_db}")
                print(f"  买家昵称: {buyer_nick}")
                print(f"  用户ID: {user_id}")
                print(f"  充值金额: {recharge_amount:.2f}元")
                print(f"  创建时间: {created_at}")
                print(f"  更新时间: {updated_at}")
                print(f"  备注: {notes}")
                print("-" * 40)
        else:
            print(f"❌ 未找到订单号 {order_no} 的充值记录")
            print()
            print("可能的原因:")
            print("1. 该订单还没有进行充值操作")
            print("2. 充值过程中出现了异常，未能保存到数据库")
            print("3. 订单号输入错误")
        
        # 同时搜索包含该订单号的记录（模糊搜索）
        print(f"\n=== 模糊搜索包含 '{order_no}' 的记录 ===")
        fuzzy_query = """
        SELECT id, buyer_eid, order_no, buyer_nick, user_id, recharge_amount, 
               created_at, notes
        FROM blacklist 
        WHERE order_no LIKE %s OR notes LIKE %s
        ORDER BY created_at DESC
        LIMIT 5
        """
        
        search_pattern = f"%{order_no}%"
        cursor.execute(fuzzy_query, (search_pattern, search_pattern))
        fuzzy_records = cursor.fetchall()
        
        if fuzzy_records:
            print(f"找到 {len(fuzzy_records)} 条相关记录:")
            for i, record in enumerate(fuzzy_records, 1):
                id_val, buyer_eid, order_no_db, buyer_nick, user_id, recharge_amount, created_at, notes = record
                print(f"  {i}. 订单号: {order_no_db}, 金额: {recharge_amount:.2f}元, 时间: {created_at}")
        else:
            print("未找到相关记录")
        
        cursor.close()
        connection.close()
        
        return len(records) > 0
        
    except psycopg2.Error as e:
        print(f"数据库查询失败: {e}")
        return False
    except Exception as e:
        print(f"查询异常: {e}")
        return False

def search_buyer_eid_in_logs(order_no):
    """在日志文件中搜索该订单号对应的buyer_eid"""
    print(f"\n=== 在日志中搜索订单 {order_no} 的buyer_eid ===")
    
    log_files = ['manual_recharge.log', 'manual_recharge.log.1']
    
    for log_file in log_files:
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"\n检查日志文件: {log_file}")
            found_lines = []
            
            for line_num, line in enumerate(lines, 1):
                if order_no in line and 'buyer_eid' in line:
                    found_lines.append((line_num, line.strip()))
            
            if found_lines:
                print(f"找到 {len(found_lines)} 条相关日志:")
                for line_num, line in found_lines:
                    print(f"  行{line_num}: {line}")
            else:
                print("未找到相关日志")
                
        except FileNotFoundError:
            print(f"日志文件 {log_file} 不存在")
        except Exception as e:
            print(f"读取日志文件 {log_file} 失败: {e}")

if __name__ == "__main__":
    # 要搜索的订单号
    target_order_no = "4683814922930041727"
    
    print("正在搜索充值记录...")
    found = search_order_in_blacklist(target_order_no)
    
    # 搜索日志中的buyer_eid信息
    search_buyer_eid_in_logs(target_order_no)
    
    print(f"\n{'='*60}")
    if found:
        print("✅ 该订单已有充值记录保存在黑名单数据库中")
    else:
        print("❌ 该订单暂无充值记录")
        print("建议: 如果您已经对该订单进行了充值，请检查充值过程中是否有错误日志")
