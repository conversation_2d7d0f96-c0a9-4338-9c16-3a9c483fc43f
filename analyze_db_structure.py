#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import configparser
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def analyze_database_structure():
    """分析数据库表结构"""
    
    # 读取配置文件
    config = configparser.ConfigParser()
    config.read('manual_recharge_config.ini', encoding='utf-8')
    
    # 数据库配置
    DB_HOST = config['Database']['host']
    DB_PORT = config['Database']['port']
    DB_USERNAME = config['Database']['username']
    DB_PASSWORD = config['Database']['password']
    DB_NAME = config['Database']['database']
    
    print(f"=== 数据库连接信息 ===")
    print(f"主机: {DB_HOST}")
    print(f"端口: {DB_PORT}")
    print(f"用户名: {DB_USERNAME}")
    print(f"数据库: {DB_NAME}")
    print("-" * 50)
    
    try:
        # 连接数据库
        connection = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USERNAME,
            password=DB_PASSWORD,
            database=DB_NAME,
            connect_timeout=10
        )
        
        cursor = connection.cursor()
        
        # 1. 查看所有表
        print("\n=== 数据库中的所有表 ===")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        tables = cursor.fetchall()
        
        if tables:
            for table in tables:
                print(f"表名: {table[0]}")
        else:
            print("数据库中没有找到任何表")
        
        # 2. 分析每个表的结构
        for table in tables:
            table_name = table[0]
            print(f"\n=== 表结构分析: {table_name} ===")
            
            # 获取表的列信息
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_name = %s
                ORDER BY ordinal_position;
            """, (table_name,))
            
            columns = cursor.fetchall()
            if columns:
                print(f"{'列名':<20} {'数据类型':<15} {'允许空值':<10} {'默认值':<15}")
                print("-" * 70)
                for col in columns:
                    col_name, data_type, is_nullable, default_val = col
                    print(f"{col_name:<20} {data_type:<15} {is_nullable:<10} {str(default_val or ''):<15}")
            else:
                print("未找到列信息")
            
            # 获取表的索引信息
            cursor.execute("""
                SELECT indexname, indexdef
                FROM pg_indexes
                WHERE tablename = %s;
            """, (table_name,))
            
            indexes = cursor.fetchall()
            if indexes:
                print(f"\n索引信息:")
                for idx in indexes:
                    print(f"  {idx[0]}: {idx[1]}")
            
            # 获取表的记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"\n记录数: {count}")
            
            # 如果是充值记录表，显示最近的几条记录
            if table_name == 'recharge_records' and count > 0:
                print(f"\n最近的5条充值记录:")
                cursor.execute(f"""
                    SELECT id, recharge_time, order_no, user_id, amount, status
                    FROM {table_name}
                    ORDER BY recharge_time DESC
                    LIMIT 5;
                """)
                recent_records = cursor.fetchall()
                for record in recent_records:
                    print(f"  ID: {record[0]}, 时间: {record[1]}, 订单: {record[2]}, 用户: {record[3]}, 金额: {record[4]}, 状态: {record[5]}")
        
        # 3. 检查数据库连接状态
        print(f"\n=== 数据库连接状态 ===")
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"PostgreSQL版本: {version}")
        
        cursor.execute("SELECT current_database(), current_user, inet_server_addr(), inet_server_port();")
        db_info = cursor.fetchone()
        print(f"当前数据库: {db_info[0]}")
        print(f"当前用户: {db_info[1]}")
        print(f"服务器地址: {db_info[2]}")
        print(f"服务器端口: {db_info[3]}")
        
        cursor.close()
        connection.close()
        
        print(f"\n=== 数据库分析完成 ===")
        
    except psycopg2.Error as e:
        print(f"数据库连接或查询失败: {e}")
        return False
    
    return True

def test_recharge_records_query():
    """测试充值记录查询功能"""
    
    # 读取配置文件
    config = configparser.ConfigParser()
    config.read('manual_recharge_config.ini', encoding='utf-8')
    
    # 数据库配置
    DB_HOST = config['Database']['host']
    DB_PORT = config['Database']['port']
    DB_USERNAME = config['Database']['username']
    DB_PASSWORD = config['Database']['password']
    DB_NAME = config['Database']['database']
    
    print(f"\n=== 测试充值记录查询 ===")
    
    try:
        # 连接数据库
        connection = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USERNAME,
            password=DB_PASSWORD,
            database=DB_NAME,
            connect_timeout=10
        )
        
        cursor = connection.cursor()
        
        # 测试查询充值记录表是否存在
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'recharge_records'
            );
        """)
        
        table_exists = cursor.fetchone()[0]
        print(f"充值记录表存在: {table_exists}")
        
        if table_exists:
            # 测试查询所有记录
            cursor.execute("""
                SELECT COUNT(*) FROM recharge_records;
            """)
            total_count = cursor.fetchone()[0]
            print(f"总记录数: {total_count}")
            
            if total_count > 0:
                # 测试查询最近的记录
                cursor.execute("""
                    SELECT id, recharge_time, order_no, buyer_nick, user_id, 
                           amount, recharge_type, status, failure_reason
                    FROM recharge_records
                    ORDER BY recharge_time DESC
                    LIMIT 3;
                """)
                
                records = cursor.fetchall()
                print(f"\n最近的3条记录:")
                for record in records:
                    print(f"  ID: {record[0]}")
                    print(f"  时间: {record[1]}")
                    print(f"  订单号: {record[2]}")
                    print(f"  买家昵称: {record[3]}")
                    print(f"  用户ID: {record[4]}")
                    print(f"  金额: {record[5]}")
                    print(f"  类型: {record[6]}")
                    print(f"  状态: {record[7]}")
                    print(f"  失败原因: {record[8] or '无'}")
                    print("  " + "-" * 40)
        
        cursor.close()
        connection.close()
        
    except psycopg2.Error as e:
        print(f"查询测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("开始分析数据库结构...")
    
    # 分析数据库结构
    success = analyze_database_structure()
    
    if success:
        # 测试充值记录查询
        test_recharge_records_query()
    
    print("\n分析完成！")
