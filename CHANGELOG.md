# 更新日志

## v2.0.0 - 2024-01-XX

### 🎉 重大更新：表格化界面

#### 新增功能
- **订单列表展示**：自动拉取最近7天的所有订单，以表格形式展示
- **智能用户匹配**：选择订单后自动通过收货手机号查询对应的用户ID
- **双击选择操作**：双击订单行即可选择订单并自动填入相关信息
- **预计算充值金额**：订单列表中直接显示预计充值金额
- **批量操作支持**：每个订单都可以独立进行充值操作

#### 界面改进
- **左右分栏布局**：左侧订单列表，右侧操作区域
- **表格式展示**：订单号、实付金额、收货手机号、买家昵称、预计充值金额一目了然
- **实时状态显示**：订单获取状态、用户查询状态、充值操作状态实时更新
- **操作日志优化**：右侧独立的日志区域，便于查看操作记录

#### 功能优化
- **自动刷新**：启动时自动获取订单列表
- **智能过滤**：只显示满足最低金额要求的订单
- **用户体验**：减少手动输入，提高操作效率
- **错误处理**：完善的异常处理和用户提示

#### 移除功能
- 移除手动输入订单号的方式
- 移除订单号输入框和相关查询逻辑
- 简化用户界面，专注核心功能

---

## v1.0.0 - 2024-01-XX

### 🚀 初始版本

#### 核心功能
- **手动充值**：支持输入订单号和用户ID进行手动充值
- **订单查询**：根据订单号查询订单信息和金额
- **用户查询**：根据用户ID查询用户类型和信息
- **老用户识别**：自动识别老用户并进行特殊处理
- **消息发送**：充值成功后自动发送通知消息
- **充值记录**：保存详细的充值记录到文件

#### 技术特性
- **Token管理**：自动获取和刷新访问令牌
- **错误重试**：网络请求失败时自动重试
- **配置管理**：支持配置文件和界面动态调整
- **日志记录**：详细的操作日志和错误记录
- **多线程**：后台线程处理网络请求，避免界面卡顿

#### 界面特性
- **Tkinter GUI**：简洁的图形用户界面
- **实时日志**：操作日志实时显示
- **状态提示**：操作状态和结果实时反馈
- **配置界面**：支持在线调整配置参数

---

## 技术栈

- **Python 3.7+**
- **Tkinter** - GUI界面
- **requests** - HTTP请求
- **threading** - 多线程处理
- **logging** - 日志记录
- **configparser** - 配置管理

## 兼容性

- **Windows** ✅
- **Linux** ✅  
- **macOS** ✅

## 依赖要求

```
requests>=2.25.0
configparser>=5.0.0
```

## 安装和使用

详见 [README.md](README.md) 文件。
