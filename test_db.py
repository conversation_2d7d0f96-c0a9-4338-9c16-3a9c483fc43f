#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from blacklist_db import get_blacklist_db, add_buyer_to_blacklist, check_buyer_in_blacklist, get_blacklist_statistics

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_database_connection():
    """测试数据库连接和基本功能"""
    try:
        print("=== 测试数据库连接 ===")
        
        # 获取数据库实例
        db = get_blacklist_db()
        print("✓ 数据库连接成功")
        
        # 测试添加黑名单记录
        print("\n=== 测试添加黑名单记录 ===")
        test_buyer_eid = "test_buyer_123456"
        test_order_no = "TEST_ORDER_001"
        test_buyer_nick = "测试买家"
        test_user_id = "12345"
        test_recharge_amount = 10.50
        
        record_id = add_buyer_to_blacklist(
            buyer_eid=test_buyer_eid,
            order_no=test_order_no,
            buyer_nick=test_buyer_nick,
            user_id=test_user_id,
            recharge_amount=test_recharge_amount,
            notes="测试记录"
        )
        print(f"✓ 成功添加测试记录，ID: {record_id}")
        
        # 测试查询黑名单
        print("\n=== 测试查询黑名单 ===")
        is_blacklisted = check_buyer_in_blacklist(test_buyer_eid)
        print(f"✓ 查询结果: {test_buyer_eid} {'在' if is_blacklisted else '不在'}黑名单中")
        
        # 测试不存在的记录
        non_exist_eid = "non_exist_buyer_999"
        is_blacklisted_2 = check_buyer_in_blacklist(non_exist_eid)
        print(f"✓ 查询结果: {non_exist_eid} {'在' if is_blacklisted_2 else '不在'}黑名单中")
        
        # 测试统计信息
        print("\n=== 测试统计信息 ===")
        stats = get_blacklist_statistics()
        print(f"✓ 黑名单统计: 总计 {stats['total']} 条记录，今日新增 {stats['today']} 条")
        
        # 测试重复添加（应该更新现有记录）
        print("\n=== 测试重复添加（更新记录） ===")
        updated_record_id = add_buyer_to_blacklist(
            buyer_eid=test_buyer_eid,
            order_no="UPDATED_ORDER_002",
            buyer_nick="更新的测试买家",
            user_id="54321",
            recharge_amount=20.80,
            notes="更新的测试记录"
        )
        print(f"✓ 成功更新记录，ID: {updated_record_id}")
        
        print("\n=== 所有测试通过 ===")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        logging.error(f"数据库测试失败: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = test_database_connection()
    if success:
        print("\n数据库功能测试完成，一切正常！")
    else:
        print("\n数据库功能测试失败，请检查配置和网络连接。")
