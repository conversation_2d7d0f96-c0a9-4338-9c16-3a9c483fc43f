#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import configparser
from datetime import datetime

# 读取配置文件
config = configparser.ConfigParser()
config.read('manual_recharge_config.ini', encoding='utf-8')

# 数据库配置
DB_HOST = config['Database']['host']
DB_PORT = config['Database']['port']
DB_USERNAME = config['Database']['username']
DB_PASSWORD = config['Database']['password']
DB_NAME = config['Database']['database']

def query_blacklist():
    """查询黑名单数据库中的所有记录"""
    try:
        # 连接数据库
        connection = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USERNAME,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        
        cursor = connection.cursor()
        
        # 查询所有记录
        query = """
        SELECT id, buyer_eid, order_no, buyer_nick, user_id, recharge_amount, 
               created_at, updated_at, notes
        FROM blacklist 
        ORDER BY created_at DESC
        """
        
        cursor.execute(query)
        records = cursor.fetchall()
        
        print("=== 黑名单数据库查询结果 ===")
        print(f"总记录数: {len(records)}")
        print()
        
        if records:
            print("详细记录:")
            print("-" * 120)
            print(f"{'ID':<5} {'buyer_eid':<25} {'订单号':<20} {'买家昵称':<15} {'用户ID':<8} {'充值金额':<10} {'创建时间':<20} {'备注':<20}")
            print("-" * 120)
            
            for record in records:
                id_val, buyer_eid, order_no, buyer_nick, user_id, recharge_amount, created_at, updated_at, notes = record
                
                # 格式化时间
                created_time = created_at.strftime("%Y-%m-%d %H:%M:%S") if created_at else "N/A"
                
                # 处理可能的None值
                buyer_eid = buyer_eid or "N/A"
                order_no = order_no or "N/A"
                buyer_nick = buyer_nick or "N/A"
                user_id = user_id or "N/A"
                recharge_amount = f"{recharge_amount:.2f}" if recharge_amount else "0.00"
                notes = notes or "N/A"
                
                print(f"{id_val:<5} {buyer_eid:<25} {order_no:<20} {buyer_nick:<15} {user_id:<8} {recharge_amount:<10} {created_time:<20} {notes:<20}")
        else:
            print("数据库中暂无记录")
        
        # 查询今日新增记录数
        today_query = "SELECT COUNT(*) FROM blacklist WHERE DATE(created_at) = CURRENT_DATE"
        cursor.execute(today_query)
        today_count = cursor.fetchone()[0]
        
        print(f"\n今日新增记录: {today_count} 条")
        
        cursor.close()
        connection.close()
        
        return True
        
    except psycopg2.Error as e:
        print(f"数据库查询失败: {e}")
        return False
    except Exception as e:
        print(f"查询异常: {e}")
        return False

def query_specific_buyer_eid(buyer_eid):
    """查询特定buyer_eid的记录"""
    try:
        connection = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USERNAME,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        
        cursor = connection.cursor()
        
        query = """
        SELECT id, buyer_eid, order_no, buyer_nick, user_id, recharge_amount, 
               created_at, updated_at, notes
        FROM blacklist 
        WHERE buyer_eid = %s
        """
        
        cursor.execute(query, (buyer_eid,))
        record = cursor.fetchone()
        
        if record:
            print(f"\n=== 查询buyer_eid: {buyer_eid} ===")
            id_val, buyer_eid, order_no, buyer_nick, user_id, recharge_amount, created_at, updated_at, notes = record
            
            print(f"ID: {id_val}")
            print(f"buyer_eid: {buyer_eid}")
            print(f"订单号: {order_no}")
            print(f"买家昵称: {buyer_nick}")
            print(f"用户ID: {user_id}")
            print(f"充值金额: {recharge_amount:.2f}元")
            print(f"创建时间: {created_at}")
            print(f"更新时间: {updated_at}")
            print(f"备注: {notes}")
        else:
            print(f"\n未找到buyer_eid为 {buyer_eid} 的记录")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"查询特定记录失败: {e}")

if __name__ == "__main__":
    print("正在查询黑名单数据库...")
    success = query_blacklist()
    
    if success:
        print("\n" + "="*50)
        # 可以查询特定的buyer_eid
        specific_eid = input("\n如果要查询特定的buyer_eid，请输入（直接回车跳过）: ").strip()
        if specific_eid:
            query_specific_buyer_eid(specific_eid)
    else:
        print("查询失败，请检查数据库连接")
